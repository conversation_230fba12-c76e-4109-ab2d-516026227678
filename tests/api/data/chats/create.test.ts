//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { ConversationBusinessLogicInterface } from "@/lib/repositories/conversations/interface"
import { ConversationBusinessLogic } from "@/lib/repositories/conversations/BusinessLogic"
import { MongoConversationRepository } from "@/lib/repositories/conversations/MongoRepository"
import { TestConversationDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import { implHandleCreateConversation } from "@/app/api/v1/conversations/impl"
import {
  createFullConversation,
  createMinimalConversation,
  createConversationWithDescription,
  createConversationWithTags,
  createConversationWithWhitespace,
  createDuplicateConversation,
  createSecondDuplicateConversation,
  createInvalidConversation,
  createConversationWithManyTags,
  createConversationWithoutDescription,
  createConversationWithEmptyTags,
} from "./object_creator"

describe("Create Conversation API Tests", () => {
  let businessLogic: ConversationBusinessLogicInterface
  let dbRepository: TestConversationDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Conversation")
    await driver.connect()
    const originalDb = new MongoConversationRepository(driver)
    dbRepository = new TestConversationDBRepositoryWrapper(originalDb, driver)
    businessLogic = new ConversationBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("POST /api/v1/conversations", () => {
    it("should successfully create a new conversations with all fields", async () => {
      const conversationsData = crConversationFullConversation()

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(
        conversationsData.STRING_FIELD,
      )
      expect(result.body.data?.ARRAY_FIELD2).toBe(
        conversationsData.ARRAY_FIELD2,
      )
      expect(result.body.data?.ARRAY_FIELD).toEqual(
        conversationsData.ARRAY_FIELD,
      )
      expect(result.body.data?.variables).toEqual(conversationsData.variables)
      expect(result.body.data?.tags).toEqual(conversationsData.tags)
      expect(result.body.data?.isActive).toBe(conversationsData.isActive)
      expect(result.body.data?.id).toBeDefined()
      expect(result.body.data?.createdAt).toBeDefined()
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should successfully create a conversations with minimal required fields", async () => {
      const conversationsData = creatConversationimalConversation()

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(
        conversationsData.STRING_FIELD,
      )
      expect(result.body.data?.ARRAY_FIELD).toEqual(
        conversationsData.ARRAY_FIELD,
      )
      expect(result.body.data?.variables).toEqual(conversationsData.variables)
      expect(result.body.data?.isActive).toBe(true) // Should default to true
      expect(result.body.data?.tags).toEqual([])
    })

    it("should create conversation with ARRAY_FIELD2", async () => {
      const conversationsData = createConversationWithDescription()

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.ARRAY_FIELD2).toBe(
        conversationsData.ARRAY_FIELD2,
      )
    })

    it("should create conversation with tags", async () => {
      const conversationsData = createConversationWithTags()

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.tags).toEqual(conversationsData.tags)
    })

    it("should trim whitespace from STRING_FIELD", async () => {
      const conversationsData = createConversationWithWhitespace()

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Conversation")
    })

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first conversations
      const conversationsData1 = createDConversationcateConversation()
      await implHandleCreateConversation(conversationsData1, businessLogic)

      // Try to create second conversations with same STRING_FIELD
      const conversationsData2 = createSecondDConversationcateConversation()
      const result = await implHandleCreateConversation(
        conversationsData2,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Conversation with the same STRING_FIELD already exists",
      )
    })

    it("should fail with missing STRING_FIELD", async () => {
      const conversationsData = {
        ARRAY_FIELD2: "+6281234567890",
      }

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with missing ARRAY_FIELD2", async () => {
      const conversationsData = creatConversationalidConversation(
        "missing-ARRAY_FIELD2",
      )

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with missing ARRAY_FIELD", async () => {
      const conversationsData = creatConversationalidConversation(
        "missing-ARRAY_FIELD",
      )

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
      expect(result.body.error![0]).toContain("ARRAY_FIELD")
    })

    it("should fail with missing variables", async () => {
      const conversationsData =
        creatConversationalidConversation("missing-variables")

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
      expect(result.body.error![0]).toContain("variables")
    })

    it("should fail with empty ARRAY_FIELD array", async () => {
      const conversationsData =
        creatConversationalidConversation("empty-ARRAY_FIELD")

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should fail with empty variables array", async () => {
      const conversationsData =
        creatConversationalidConversation("empty-variables")

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(Array.isArray(result.body.error)).toBe(true)
    })

    it("should create conversation with many tags", async () => {
      const conversationsData = createConversationWithManyTags()

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.tags).toEqual(conversationsData.tags)
    })

    it("should handle optional ARRAY_FIELD2", async () => {
      const conversationsData = createConversationWithoutDescription()

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.ARRAY_FIELD2).toBe("")
    })

    it("should handle empty arrays for tags", async () => {
      const conversationsData = createConversationWithEmptyTags()

      const result = await implHandleCreateConversation(
        conversationsData,
        businessLogic,
      )

      expect(result.status).toBe(201)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.tags).toEqual(conversationsData.tags)
    })
  })
})
