import {
  TEMPLATE_CAPITALIZEDCreateInput,
  TEMPLATE_CAPITALIZEDUpdateInput,
} from "@/lib/repositories/TEMPLATE_CAMELCASEDs/interface"

/**
 * Factory functions for creating test TEMPLATE_CAPITALIZED objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createTEMPLATE_CAPITALIZED(
  variant: number,
): TEMPLATE_CAPITALIZEDCreateInput {
  const baseTEMPLATE_CAPITALIZEDs: Record<
    number,
    TEMPLATE_CAPITALIZEDCreateInput
  > = {
    1: {
      STRING_FIELD: "Customer Support TEMPLATE_CAPITALIZED",
      STRING_FIELD2:
        "TEMPLATE_CAPITALIZED for handling customer support requests",
      ARRAY_FIELD2: [
        "TEMPLATE_CAMELCASED_message_contains('help')",
        "time_between('09:00', '17:00')",
      ],
      ARRAY_FIELD: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin",
    },
    2: {
      STRING_FIELD: "Simple TEMPLATE_CAPITALIZED",
      ARRAY_FIELD2: ["always_true"],
      ARRAY_FIELD: ["log_message"],
      createdBy: "admin",
    },
    3: {
      STRING_FIELD: "Test TEMPLATE_CAPITALIZED",
      STRING_FIELD2: "A test TEMPLATE_CAMELCASED with STRING_FIELD2",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    4: {
      STRING_FIELD: "Tagged TEMPLATE_CAPITALIZED",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin",
    },
    5: {
      STRING_FIELD: "John Doe TEMPLATE_CAPITALIZED",
      STRING_FIELD2: "TEMPLATE_CAPITALIZED for John Doe processing",
      ARRAY_FIELD2: [
        "TEMPLATE_CAMELCASED_STRING_FIELD_contains('john')",
        "time_between('09:00', '17:00')",
      ],
      ARRAY_FIELD: ["assign_to_support", "send_ARRAY_FIELD"],
      tags: ["Customer", "VIP"],
      createdBy: "admin",
    },
    6: {
      STRING_FIELD: "Jane Smith TEMPLATE_CAPITALIZED",
      STRING_FIELD2: "TEMPLATE_CAPITALIZED for Jane Smith processing",
      ARRAY_FIELD2: [
        "TEMPLATE_CAMELCASED_STRING_FIELD_contains('jane')",
        "priority_high",
      ],
      ARRAY_FIELD: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin",
    },
    7: {
      STRING_FIELD: "Bob Johnson TEMPLATE_CAPITALIZED",
      STRING_FIELD2: "TEMPLATE_CAPITALIZED for Bob Johnson processing",
      ARRAY_FIELD2: [
        "TEMPLATE_CAMELCASED_STRING_FIELD_contains('bob')",
        "vip_customer",
      ],
      ARRAY_FIELD: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin",
    },
    8: {
      STRING_FIELD: "Alice Brown TEMPLATE_CAPITALIZED",
      STRING_FIELD2: "TEMPLATE_CAPITALIZED for Alice Brown processing",
      ARRAY_FIELD2: [
        "TEMPLATE_CAMELCASED_STRING_FIELD_contains('alice')",
        "lead_qualification",
      ],
      ARRAY_FIELD: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin",
    },
  }

  if (!baseTEMPLATE_CAPITALIZEDs[variant]) {
    throw new Error(
      `TEMPLATE_CAPITALIZED variant ${variant} not found. Available variants: ${Object.keys(baseTEMPLATE_CAPITALIZEDs).join(", ")}`,
    )
  }

  return { ...baseTEMPLATE_CAPITALIZEDs[variant] }
}

// Specialized creator functions for specific test scenarios
export function createMinimalTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return createTEMPLATE_CAPITALIZED(2)
}

export function createFullTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return createTEMPLATE_CAPITALIZED(1)
}

export function createTEMPLATE_CAPITALIZEDWithDescription(): TEMPLATE_CAPITALIZEDCreateInput {
  return createTEMPLATE_CAPITALIZED(3)
}

export function createTEMPLATE_CAPITALIZEDWithTags(): TEMPLATE_CAPITALIZEDCreateInput {
  return createTEMPLATE_CAPITALIZED(4)
}

// Creator for multiple TEMPLATE_CAMELCASEDs (useful for bulk operations and search tests)
export function createMultipleTEMPLATE_CAPITALIZEDs(): TEMPLATE_CAPITALIZEDCreateInput[] {
  return [
    createTEMPLATE_CAPITALIZED(5), // John Doe TEMPLATE_CAPITALIZED
    createTEMPLATE_CAPITALIZED(6), // Jane Smith TEMPLATE_CAPITALIZED
    createTEMPLATE_CAPITALIZED(7), // Bob Johnson TEMPLATE_CAPITALIZED
    createTEMPLATE_CAPITALIZED(8), // Alice Brown TEMPLATE_CAPITALIZED
  ]
}

// Creator for simple test TEMPLATE_CAMELCASEDs (useful for basic CRUD operations)
export function createSimpleTEMPLATE_CAPITALIZEDs(): TEMPLATE_CAPITALIZEDCreateInput[] {
  return [
    {
      STRING_FIELD: "A",
      ARRAY_FIELD2: ["1"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "B",
      ARRAY_FIELD2: ["2"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "C",
      ARRAY_FIELD2: ["3"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
    },
  ]
}

// Creator for TEMPLATE_CAMELCASEDs with specific tags (useful for filtering tests)
export function createTEMPLATE_CAPITALIZEDsWithTags(): TEMPLATE_CAPITALIZEDCreateInput[] {
  return [
    {
      STRING_FIELD: "John Doe",
      ARRAY_FIELD2: ["x"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
      tags: ["Customer", "VIP"],
    },
    {
      STRING_FIELD: "Jane Smith",
      ARRAY_FIELD2: ["y"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
      tags: ["Lead", "Potential"],
    },
    {
      STRING_FIELD: "Bob Johnson",
      ARRAY_FIELD2: ["z"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
      tags: ["Customer"],
    },
    {
      STRING_FIELD: "Alice Brown",
      ARRAY_FIELD2: ["a"],
      ARRAY_FIELD: ["d"],
      createdBy: "admin",
      tags: ["VIP"],
    },
  ]
}

// Update data creators
export function createTEMPLATE_CAPITALIZEDUpdate(
  variant: number,
): TEMPLATE_CAPITALIZEDUpdateInput {
  const baseUpdates: Record<number, TEMPLATE_CAPITALIZEDUpdateInput> = {
    1: {
      STRING_FIELD: "Updated TEMPLATE_CAPITALIZED",
      STRING_FIELD2: "Updated STRING_FIELD2",
      ARRAY_FIELD2: ["updated_condition"],
      ARRAY_FIELD: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin",
    },
    2: {
      STRING_FIELD: "New Name",
      updatedBy: "admin",
    },
    3: {
      STRING_FIELD2: "Updated STRING_FIELD2 only",
      updatedBy: "admin",
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin",
    },
    5: {
      isActive: false,
      updatedBy: "admin",
    },
  }

  if (!baseUpdates[variant]) {
    throw new Error(
      `TEMPLATE_CAPITALIZED update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(", ")}`,
    )
  }

  return { ...baseUpdates[variant] }
}

// Specialized update creators
export function createFullTEMPLATE_CAPITALIZEDUpdate(): TEMPLATE_CAPITALIZEDUpdateInput {
  return createTEMPLATE_CAPITALIZEDUpdate(1)
}

export function createNameOnlyUpdate(): TEMPLATE_CAPITALIZEDUpdateInput {
  return createTEMPLATE_CAPITALIZEDUpdate(2)
}

export function createDescriptionOnlyUpdate(): TEMPLATE_CAPITALIZEDUpdateInput {
  return createTEMPLATE_CAPITALIZEDUpdate(3)
}

export function createTagsOnlyUpdate(): TEMPLATE_CAPITALIZEDUpdateInput {
  return createTEMPLATE_CAPITALIZEDUpdate(4)
}

export function createStatusOnlyUpdate(): TEMPLATE_CAPITALIZEDUpdateInput {
  return createTEMPLATE_CAPITALIZEDUpdate(5)
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(
  type:
    | "empty-STRING_FIELD"
    | "empty-ARRAY_FIELD2"
    | "empty-ARRAY_FIELD"
    | "empty-object",
): any {
  const invalidUpdates = {
    "empty-STRING_FIELD": {
      STRING_FIELD: "",
      updatedBy: "admin",
    },
    "empty-ARRAY_FIELD2": {
      ARRAY_FIELD2: [],
      updatedBy: "admin",
    },
    "empty-ARRAY_FIELD": {
      ARRAY_FIELD: [],
      updatedBy: "admin",
    },
    "empty-object": {},
  }

  return invalidUpdates[type]
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): TEMPLATE_CAPITALIZEDUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    updatedBy: "admin",
  }
}

// Update for duplicate STRING_FIELD testing
export function createDuplicateNameUpdate(
  existingName: string,
): TEMPLATE_CAPITALIZEDUpdateInput {
  return {
    STRING_FIELD: existingName,
    updatedBy: "admin",
  }
}

// Update with same STRING_FIELD (no change scenario)
export function createSameNameUpdate(): TEMPLATE_CAPITALIZEDUpdateInput {
  return {
    STRING_FIELD: "Simple TEMPLATE_CAPITALIZED", // Same as createMinimalTEMPLATE_CAPITALIZED
    STRING_FIELD2: "Updated STRING_FIELD2",
    updatedBy: "admin",
  }
}

// TEMPLATE_CAPITALIZED for soft delete testing
export function createTEMPLATE_CAPITALIZEDForSoftDelete(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "To Be Deleted",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin",
  }
}

// Update for soft deleted TEMPLATE_CAMELCASED testing
export function createUpdateForSoftDeleted(): TEMPLATE_CAPITALIZEDUpdateInput {
  return {
    STRING_FIELD: "Should Not Work",
    updatedBy: "admin",
  }
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): TEMPLATE_CAPITALIZEDUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    STRING_FIELD2: "   Trimmed Description   ",
    ARRAY_FIELD2: ["   trimmed_condition   "],
    ARRAY_FIELD: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin",
  }
}

// TEMPLATE_CAPITALIZED for trimming test
export function createTEMPLATE_CAPITALIZEDForTrimming(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Original TEMPLATE_CAPITALIZED",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin",
  }
}

// TEMPLATE_CAPITALIZED for active status testing
export function createActiveTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Active TEMPLATE_CAPITALIZED",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    isActive: true,
    createdBy: "admin",
  }
}

// Update for status change testing
export function createStatusChangeUpdate(): TEMPLATE_CAPITALIZEDUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin",
  }
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllTEMPLATE_CAPITALIZEDs
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" }
}

export function createSearchByDescriptionParams() {
  return { search: "processing" }
}

export function createEmptySearchParams() {
  return { search: "" }
}

export function createWhitespaceSearchParams() {
  return { search: "   " }
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" }
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }],
  }
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }],
  }
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2,
  }
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sort: [{ field: "STRING_FIELD", direction: "ASC" as const }],
  }
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP",
  }
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true }
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }],
  }
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }],
  }
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }],
  }
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" }
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" }
}

export function createUndefinedSearchParams() {
  return { search: undefined }
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }],
  }
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }],
  }
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }],
  }
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// TEMPLATE_CAPITALIZED for retry delete testing
export function createRetryDeleteTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Retry Delete",
    ARRAY_FIELD2: ["attempt"],
    ARRAY_FIELD: ["log"],
    createdBy: "admin",
  }
}

// ========================================
// CREATORS FOR SPECIAL CASES (TEMPLATE_CAPITALIZED-specific)
// ========================================

// TEMPLATE_CAPITALIZED with special characters and unicode
export function createSpecialCharacterTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "José María O'Connor",
    STRING_FIELD2: "Handles unicode 🎉 & symbols",
    ARRAY_FIELD2: ["STRING_FIELD.includes('José')"],
    ARRAY_FIELD: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin",
  }
}

// TEMPLATE_CAPITALIZED with very long ARRAY_FIELD2 (TEMPLATE_CAPITALIZED-specific test)
export function createLongContentTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD:
      "Very Long TEMPLATE_CAPITALIZED Name That Exceeds Normal Length Expectations And Tests System Limits",
    STRING_FIELD2:
      "This is a very long STRING_FIELD2 that tests how the system handles extensive text ARRAY_FIELD2 in TEMPLATE_CAMELCASED STRING_FIELD2s. It includes multiple sentences and should test the limits of what the system can handle in terms of ARRAY_FIELD2 length and processing.",
    ARRAY_FIELD2: [
      "TEMPLATE_CAMELCASED.message.length > 1000",
      "TEMPLATE_CAMELCASED.message.includes('very long query with lots of details')",
      "TEMPLATE_CAMELCASED.session.duration > 3600",
    ],
    ARRAY_FIELD: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist",
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin",
  }
}

// TEMPLATE_CAPITALIZED with edge case ARRAY_FIELD2 (TEMPLATE_CAPITALIZED-specific)
export function createEdgeCaseConditionsTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Edge Case Conditions",
    STRING_FIELD2: "Tests complex condition parsing",
    ARRAY_FIELD2: [
      "TEMPLATE_CAMELCASED.age >= 18 && TEMPLATE_CAMELCASED.age <= 65",
      "TEMPLATE_CAMELCASED.location.country === 'US' || TEMPLATE_CAMELCASED.location.country === 'CA'",
      "TEMPLATE_CAMELCASED.preferences.notifications === true",
    ],
    ARRAY_FIELD: [
      "apply_regional_TEMPLATE_CAMELCASEDs",
      "send_age_appropriate_ARRAY_FIELD2",
    ],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin",
  }
}

// TEMPLATE_CAPITALIZED with complex ARRAY_FIELD (TEMPLATE_CAPITALIZED-specific)
export function createComplexActionsTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Complex Actions TEMPLATE_CAPITALIZED",
    STRING_FIELD2: "Tests complex action execution",
    ARRAY_FIELD2: ["trigger_complex_workflow"],
    ARRAY_FIELD: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('TEMPLATE_CAMELCASED_stats', {last_interaction: now()})",
      "ARRAY_FIELD.send(template='complex_notification', to=TEMPLATE_CAMELCASED.ARRAY_FIELD)",
      "analytics.track('complex_TEMPLATE_CAMELCASED_triggered', {TEMPLATE_CAMELCASED_id: this.id})",
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin",
  }
}

// TEMPLATE_CAPITALIZED with empty optional fields (TEMPLATE_CAPITALIZED-specific edge case)
export function createEmptyOptionalFieldsTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Empty Optional Fields",
    ARRAY_FIELD2: ["basic_condition"],
    ARRAY_FIELD: ["basic_action"],
    STRING_FIELD2: "",
    tags: [],
    createdBy: "admin",
  }
}

// TEMPLATE_CAPITALIZED for testing AI-specific business logic
export function createAiLogicTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "AI Decision TEMPLATE_CAPITALIZED",
    STRING_FIELD2: "Tests AI-specific decision making logic",
    ARRAY_FIELD2: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100",
    ],
    ARRAY_FIELD: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model",
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin",
  }
}

// Creators for delete test scenarios
export function createComplexTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Complex TEMPLATE_CAPITALIZED",
    STRING_FIELD2: "Full field test",
    ARRAY_FIELD2: ["TEMPLATE_CAMELCASED.role == 'admin'"],
    ARRAY_FIELD: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin",
  }
}

export function createMinimalDeleteTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Minimal TEMPLATE_CAPITALIZED",
    ARRAY_FIELD2: ["is.loggedIn"],
    ARRAY_FIELD: ["alert"],
    createdBy: "admin",
  }
}

// TEMPLATE_CAPITALIZEDs for testing deletion effects on other TEMPLATE_CAMELCASEDs
export function createTEMPLATE_CAPITALIZEDsForDeletionTest(): TEMPLATE_CAPITALIZEDCreateInput[] {
  return [
    {
      STRING_FIELD: "Keep This One",
      ARRAY_FIELD2: ["x"],
      ARRAY_FIELD: ["a"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Delete This One",
      ARRAY_FIELD2: ["y"],
      ARRAY_FIELD: ["b"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "Keep This Too",
      ARRAY_FIELD2: ["z"],
      ARRAY_FIELD: ["c"],
      createdBy: "admin",
    },
  ]
}

// Creators for bulk operations testing
export function createExistingTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Existing TEMPLATE_CAPITALIZED",
    STRING_FIELD2: "An existing TEMPLATE_CAMELCASED",
    ARRAY_FIELD2: ["TEMPLATE_CAPITALIZED says test"],
    ARRAY_FIELD: ["Show test response"],
    createdBy: "admin",
  }
}

export function createDuplicateTEMPLATE_CAPITALIZEDsForBulk(): TEMPLATE_CAPITALIZEDCreateInput[] {
  return [
    {
      STRING_FIELD: "Existing TEMPLATE_CAPITALIZED", // Duplicate STRING_FIELD
      STRING_FIELD2: "Another TEMPLATE_CAMELCASED with same STRING_FIELD",
      ARRAY_FIELD2: ["TEMPLATE_CAPITALIZED says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "New TEMPLATE_CAPITALIZED",
      STRING_FIELD2: "A new TEMPLATE_CAMELCASED",
      ARRAY_FIELD2: ["TEMPLATE_CAPITALIZED says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
  ]
}

// TEMPLATE_CAPITALIZEDs for bulk update testing
export function createTEMPLATE_CAPITALIZEDsForBulkUpdate(): TEMPLATE_CAPITALIZEDCreateInput[] {
  return [
    {
      STRING_FIELD: "TEMPLATE_CAPITALIZED 1",
      STRING_FIELD2: "First TEMPLATE_CAMELCASED",
      ARRAY_FIELD2: ["TEMPLATE_CAPITALIZED says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "TEMPLATE_CAPITALIZED 2",
      STRING_FIELD2: "Second TEMPLATE_CAMELCASED",
      ARRAY_FIELD2: ["TEMPLATE_CAPITALIZED says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
  ]
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      STRING_FIELD: "Updated TEMPLATE_CAPITALIZED 1",
      STRING_FIELD2: "Updated first TEMPLATE_CAMELCASED",
      updatedBy: "admin",
    },
    {
      STRING_FIELD: "Updated TEMPLATE_CAPITALIZED 2",
      STRING_FIELD2: "Updated second TEMPLATE_CAMELCASED",
      updatedBy: "admin",
    },
  ]
}

// TEMPLATE_CAPITALIZEDs for bulk delete testing
export function createTEMPLATE_CAPITALIZEDsForBulkDelete(): TEMPLATE_CAPITALIZEDCreateInput[] {
  return [
    {
      STRING_FIELD: "TEMPLATE_CAPITALIZED 1",
      STRING_FIELD2: "First TEMPLATE_CAMELCASED",
      ARRAY_FIELD2: ["TEMPLATE_CAPITALIZED says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "TEMPLATE_CAPITALIZED 2",
      STRING_FIELD2: "Second TEMPLATE_CAMELCASED",
      ARRAY_FIELD2: ["TEMPLATE_CAPITALIZED says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin",
    },
    {
      STRING_FIELD: "TEMPLATE_CAPITALIZED 3",
      STRING_FIELD2: "Third TEMPLATE_CAMELCASED",
      ARRAY_FIELD2: ["TEMPLATE_CAPITALIZED asks question"],
      ARRAY_FIELD: ["Show help"],
      createdBy: "admin",
    },
  ]
}

// Invalid data creators for validation tests
export function createInvalidTEMPLATE_CAPITALIZED(
  type:
    | "missing-STRING_FIELD"
    | "missing-ARRAY_FIELD2"
    | "missing-ARRAY_FIELD"
    | "empty-ARRAY_FIELD2"
    | "empty-ARRAY_FIELD"
    | "missing-ARRAY_FIELD2",
): any {
  const invalidTEMPLATE_CAPITALIZEDs = {
    "missing-ARRAY_FIELD2": {
      STRING_FIELD: "John Doe",
    },
    "missing-STRING_FIELD": {
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "missing-ARRAY_FIELD2": {
      STRING_FIELD: "Invalid TEMPLATE_CAPITALIZED",
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "missing-ARRAY_FIELD": {
      STRING_FIELD: "Invalid TEMPLATE_CAPITALIZED",
      ARRAY_FIELD2: ["test_condition"],
      createdBy: "admin",
    },
    "empty-ARRAY_FIELD2": {
      STRING_FIELD: "Invalid TEMPLATE_CAPITALIZED",
      ARRAY_FIELD2: [],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin",
    },
    "empty-ARRAY_FIELD": {
      STRING_FIELD: "Invalid TEMPLATE_CAPITALIZED",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: [],
      createdBy: "admin",
    },
  }

  return invalidTEMPLATE_CAPITALIZEDs[type]
}

// Creator for TEMPLATE_CAMELCASEDs with special characteristics
export function createTEMPLATE_CAPITALIZEDWithWhitespace(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "  Trimmed TEMPLATE_CAPITALIZED  ",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin",
  }
}

export function createTEMPLATE_CAPITALIZEDWithManyTags(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Multi-tag TEMPLATE_CAPITALIZED",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin",
  }
}

export function createTEMPLATE_CAPITALIZEDWithoutDescription(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "TEMPLATE_CAPITALIZED without STRING_FIELD2",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin",
  }
}

export function createTEMPLATE_CAPITALIZEDWithEmptyTags(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "TEMPLATE_CAPITALIZED with empty tags",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: [],
    createdBy: "admin",
  }
}

// Duplicate TEMPLATE_CAMELCASED creator for conflict testing
export function createDuplicateTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Duplicate TEMPLATE_CAPITALIZED",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin",
  }
}

export function createSecondDuplicateTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Duplicate TEMPLATE_CAPITALIZED", // Same STRING_FIELD as above
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin",
  }
}

// Test TEMPLATE_CAMELCASED with specific STRING_FIELD for soft delete tests
export function createTestTEMPLATE_CAPITALIZED(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Test TEMPLATE_CAPITALIZED",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin",
  }
}

export function createTestTEMPLATE_CAPITALIZED2(): TEMPLATE_CAPITALIZEDCreateInput {
  return {
    STRING_FIELD: "Test TEMPLATE_CAPITALIZED",
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin",
  }
}
