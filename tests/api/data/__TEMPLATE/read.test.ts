// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/interface"
import { TEMPLATE_CAPITALIZEDBusinessLogic } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/BusinessLogic"
import { MongoTEMPLATE_CAPITALIZEDRepository } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/MongoRepository"
import { TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateTEMPLATE_CAPITALIZED,
  implHandleGetTEMPLATE_CAPITALIZED,
  implHandleGetAllTEMPLATE_CAPITALIZEDs,
} from "@/app/api/v1/TEMPLATE_API_PATHs/impl"
import {
  createTEMPLATE_CAPITALIZED,
  createSimpleTEMPLATE_CAPITALIZEDs,
  createTEMPLATE_CAPITALIZEDsWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams,
} from "./object_creator"

describe("Read TEMPLATE_CAPITALIZED API Tests", () => {
  let businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
  let dbRepository: TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("TEMPLATE_CAPITALIZED")
    await driver.connect()
    const originalDb = new MongoTEMPLATE_CAPITALIZEDRepository(driver)
    dbRepository = new TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new TEMPLATE_CAPITALIZEDBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("GET /api/v1/TEMPLATE_API_PATHs/:id", () => {
    it("should successfully get TEMPLATE_CAMELCASED by ID", async () => {
      const TEMPLATE_CAMELCASED = createTEMPLATE_CAPITALIZED(5) // John Doe TEMPLATE_CAPITALIZED

      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASED,
        businessLogic,
      )
      const id = createResult.body.data.id

      const result = await implHandleGetTEMPLATE_CAPITALIZED(id, businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.id).toBe(id)
      expect(result.body.data?.STRING_FIELD).toBe(
        TEMPLATE_CAMELCASED.STRING_FIELD,
      )
    })

    it("should fail to get non-existent TEMPLATE_CAMELCASED", async () => {
      const result = await implHandleGetTEMPLATE_CAPITALIZED(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
    })

    it("should fail with empty TEMPLATE_CAMELCASED ID", async () => {
      const result = await implHandleGetTEMPLATE_CAPITALIZED("", businessLogic)
      expect(result.status).toBe(400)
    })

    it("should fail with whitespace-only TEMPLATE_CAMELCASED ID", async () => {
      const result = await implHandleGetTEMPLATE_CAPITALIZED(
        "   ",
        businessLogic,
      )
      expect(result.status).toBe(400)
    })
  })

  describe("GET /api/v1/TEMPLATE_API_PATHs", () => {
    it("should get all TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDs = createSimpleTEMPLATE_CAPITALIZEDs()
      for (const r of TEMPLATE_CAMELCASEDs)
        await implHandleCreateTEMPLATE_CAPITALIZED(r, businessLogic)

      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(3)
    })

    it("should return empty when no TEMPLATE_CAMELCASEDs exist", async () => {
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic)
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })
  })

  describe("GET /api/v1/TEMPLATE_API_PATHs/search", () => {
    beforeEach(async () => {
      const data = createTEMPLATE_CAPITALIZEDsWithTags()
      for (const r of data)
        await implHandleCreateTEMPLATE_CAPITALIZED(r, businessLogic)
    })

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should search by tag", async () => {
      const params = createSearchByTagParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(4)
    })
  })

  describe("GET /api/v1/TEMPLATE_API_PATHs/filters", () => {
    beforeEach(async () => {
      const data = createTEMPLATE_CAPITALIZEDsWithTags()
      for (const r of data)
        await implHandleCreateTEMPLATE_CAPITALIZED(r, businessLogic)
    })

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(2)
    })

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(200)
      expect(result.body.data?.items.length).toBe(0)
    })

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams()
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        params,
      )
      expect(result.status).toBe(400)
    })
  })
})
