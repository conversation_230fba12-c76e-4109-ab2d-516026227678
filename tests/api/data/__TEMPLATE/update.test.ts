//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/interface"
import { TEMPLATE_CAPITALIZEDBusinessLogic } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/BusinessLogic"
import { MongoTEMPLATE_CAPITALIZEDRepository } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/MongoRepository"
import { TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateTEMPLATE_CAPITALIZED,
  implHandleUpdateTEMPLATE_CAPITALIZED,
  implHandleDeleteTEMPLATE_CAPITALIZED,
} from "@/app/api/v1/TEMPLATE_API_PATHs/impl"
import {
  createFullTEMPLATE_CAPITALIZED,
  createMinimalTEMPLATE_CAPITALIZED,
  createFullTEMPLATE_CAPITALIZEDUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createTEMPLATE_CAPITALIZEDForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createTEMPLATE_CAPITALIZEDForTrimming,
  createActiveTEMPLATE_CAPITALIZED,
  createStatusChangeUpdate,
} from "./object_creator"

describe("Update TEMPLATE_CAPITALIZED API Tests", () => {
  let businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
  let dbRepository: TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("TEMPLATE_CAPITALIZED")
    await driver.connect()
    const originalDb = new MongoTEMPLATE_CAPITALIZEDRepository(driver)
    dbRepository = new TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new TEMPLATE_CAPITALIZEDBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("PUT /api/v1/TEMPLATE_API_PATHs/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createFullTEMPLATE_CAPITALIZEDUpdate()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2)
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(updateData.variables)
      expect(result.body.data?.tags).toEqual(updateData.tags)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
      expect(result.body.data?.updatedAt).toBeDefined()
    })

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD)
      expect(result.body.data?.variables).toEqual(createData.variables)
    })

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createUpdateWithWhitespace()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name")
    })

    it("should fail to update non-existent TEMPLATE_CAMELCASED", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        "507f1f77bcf86cd799439011",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("TEMPLATE_CAPITALIZED not found")
    })

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-STRING_FIELD")
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-ARRAY_FIELD")
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should fail with empty update object", async () => {
      const createData = createMinimalTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-object")
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("No data provided for update")
    })

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        "",
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("TEMPLATE_CAPITALIZED ID is required")
    })

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first TEMPLATE_CAMELCASED
      const createData1 = createMinimalTEMPLATE_CAPITALIZED()
      await implHandleCreateTEMPLATE_CAPITALIZED(createData1, businessLogic)

      // Create second TEMPLATE_CAMELCASED
      const createData2 = createFullTEMPLATE_CAPITALIZED()
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData2,
        businessLogic,
      )

      // Try to update second TEMPLATE_CAMELCASED with first TEMPLATE_CAMELCASED's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD)
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        createResult2.body.data.id,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(409)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain(
        "Another TEMPLATE_CAPITALIZED with this STRING_FIELD exists",
      )
    })

    it("should allow updating TEMPLATE_CAMELCASED with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createSameNameUpdate()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.status).toBe("success")
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD)
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2)
    })

    it("should fail to update soft-deleted TEMPLATE_CAMELCASED", async () => {
      const createData = createTEMPLATE_CAPITALIZEDForSoftDelete()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      // Soft delete the TEMPLATE_CAMELCASED
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Try to update the soft-deleted TEMPLATE_CAMELCASED
      const updateData = createUpdateForSoftDeleted()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("TEMPLATE_CAPITALIZED not found")
    })

    it("should trim all string fields when updating", async () => {
      const createData = createTEMPLATE_CAPITALIZEDForTrimming()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createUpdateWithAllFieldsWhitespace()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name")
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description")
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"])
      expect(result.body.data?.variables).toEqual(["trimmed_action"])
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"])
    })

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createInvalidUpdate("empty-variables")
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
    })

    it("should update isActive status", async () => {
      const createData = createActiveTEMPLATE_CAPITALIZED()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        createData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const updateData = createStatusChangeUpdate()
      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        updateData,
        businessLogic,
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.isActive).toBe(updateData.isActive)
    })
  })
})
