//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/interface"
import { TEMPLATE_CAPITALIZEDBusinessLogic } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/BusinessLogic"
import { MongoTEMPLATE_CAPITALIZEDRepository } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/MongoRepository"
import { TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateTEMPLATE_CAPITALIZED,
  implHandleGetTEMPLATE_CAPITALIZED,
  implHandleDeleteTEMPLATE_CAPITALIZED,
  implHandleUpdate<PERSON>MPLATE_CAPITALIZED,
  implHandleGetAllTEMPLATE_CAPITALIZEDs,
  implHandleRestoreTEMPLATE_CAPITALIZED,
} from "@/app/api/v1/TEMPLATE_API_PATHs/impl"
import {
  createTEMPLATE_CAPITALIZED,
  createTEMPLATE_CAPITALIZEDUpdate,
  createTEMPLATE_CAPITALIZEDWithDescription,
  createTestTEMPLATE_CAPITALIZED,
  createTestTEMPLATE_CAPITALIZED2,
} from "./object_creator"

describe("TEMPLATE_CAPITALIZED Soft Delete Tests", () => {
  let businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
  let dbRepository: TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("TEMPLATE_CAPITALIZED")
    await driver.connect()
    const originalDb = new MongoTEMPLATE_CAPITALIZEDRepository(driver)
    dbRepository = new TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new TEMPLATE_CAPITALIZEDBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("Soft Delete", () => {
    it("should soft delete a TEMPLATE_CAMELCASEDs by default", async () => {
      const TEMPLATE_CAMELCASEDData =
        createTEMPLATE_CAPITALIZEDWithDescription()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // TEMPLATE_CAPITALIZED should not be accessible by default
      const getResult = await implHandleGetTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(200)
      expect(getDeletedResult.body.data).not.toBeNull()

      // Count should exclude soft deleted
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(0)
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount(true)).toBe(1)
    })

    it("should hard delete when specified", async () => {
      const TEMPLATE_CAMELCASEDData =
        createTEMPLATE_CAPITALIZEDWithDescription()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
        true,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")

      // TEMPLATE_CAPITALIZED should not be accessible at all
      const getResult = await implHandleGetTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)
      const getDeletedResult = await implHandleGetTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(getDeletedResult.status).toBe(404)

      // Count should be 0 in both cases
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(0)
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount(true)).toBe(0)
    })

    it("should not include soft deleted TEMPLATE_CAMELCASEDs in getAll by default", async () => {
      const TEMPLATE_CAMELCASEDData1 = createTEMPLATE_CAPITALIZED(1)
      const TEMPLATE_CAMELCASEDData2 = createTEMPLATE_CAPITALIZED(2)

      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData1,
        businessLogic,
      )
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData2,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one TEMPLATE_CAMELCASEDs
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic)

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(1)
      expect(result.body.data?.total).toBe(1)
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id)
    })

    it("should include soft deleted TEMPLATE_CAMELCASEDs when specified", async () => {
      const TEMPLATE_CAMELCASEDData1 = createTEMPLATE_CAPITALIZED(1)
      const TEMPLATE_CAMELCASEDData2 = createTEMPLATE_CAPITALIZED(2)

      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData1,
        businessLogic,
      )
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData2,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)
      expect(createResult2.status).toBe(201)

      // Soft delete one TEMPLATE_CAMELCASEDs
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        { includeDeleted: true },
      )

      expect(result.status).toBe(200)
      expect(result.body.data?.items).toHaveLength(2)
      expect(result.body.data?.total).toBe(2)

      const deletedTEMPLATE_CAPITALIZED = result.body.data?.items.find(
        (c: any) => c.id === createResult1.body.data.id,
      )
      expect(deletedTEMPLATE_CAPITALIZED).toBeDefined()
      expect(deletedTEMPLATE_CAPITALIZED?.deletedAt).toBeDefined()
    })

    it("should not allow updating soft deleted TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDData = createTEMPLATE_CAPITALIZED(3)
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const TEMPLATE_CAMELCASEDUpdate = createTEMPLATE_CAPITALIZEDUpdate(1)

      const result = await implHandleUpdateTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        TEMPLATE_CAMELCASEDUpdate,
        businessLogic,
      )

      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
    })

    it("should not include soft deleted TEMPLATE_CAMELCASEDs in search", async () => {
      const TEMPLATE_CAMELCASEDData1 = createTEMPLATE_CAPITALIZED(3) // "Test TEMPLATE_CAPITALIZED"
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData1,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Soft delete the TEMPLATE_CAMELCASED
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Search should not find the soft deleted TEMPLATE_CAMELCASED
      const searchResult = await implHandleGetAllTEMPLATE_CAPITALIZEDs(
        businessLogic,
        { search: "Test" },
      )
      expect(searchResult.status).toBe(200)
      expect(searchResult.body.data).toHaveLength(0)
    })
  })

  describe("Restore", () => {
    it("should restore a soft deleted TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDData =
        createTEMPLATE_CAPITALIZEDWithDescription()
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Soft delete the TEMPLATE_CAMELCASEDs
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getResult = await implHandleGetTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(404)

      // Restore the TEMPLATE_CAMELCASEDs
      const restoreResult = await implHandleRestoreTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )

      expect(restoreResult.status).toBe(200)
      expect(restoreResult.body.status).toBe("success")

      // TEMPLATE_CAPITALIZED should be accessible again
      const restoredResult = await implHandleGetTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoredResult.status).toBe(200)
      expect(restoredResult.body.data?.deletedAt).toBeUndefined()

      // Count should include the restored TEMPLATE_CAMELCASEDs
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(1)
    })

    it("should fail to restore a non-existent TEMPLATE_CAMELCASEDs", async () => {
      const restoreResult = await implHandleRestoreTEMPLATE_CAPITALIZED(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a TEMPLATE_CAMELCASEDs that was never deleted", async () => {
      const TEMPLATE_CAMELCASEDData = createTEMPLATE_CAPITALIZED(3)
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const restoreResult = await implHandleRestoreTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail to restore a hard deleted TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDData = createTEMPLATE_CAPITALIZED(3)
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      // Hard delete the TEMPLATE_CAMELCASEDs
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
        true,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })

    it("should fail with empty TEMPLATE_CAMELCASEDs ID", async () => {
      const restoreResult = await implHandleRestoreTEMPLATE_CAPITALIZED(
        "",
        businessLogic,
      )
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain(
        "TEMPLATE_CAPITALIZED ID is required",
      )
    })

    it("should fail with whitespace-only TEMPLATE_CAMELCASEDs ID", async () => {
      const restoreResult = await implHandleRestoreTEMPLATE_CAPITALIZED(
        "   ",
        businessLogic,
      )
      expect(restoreResult.status).toBe(400)
      expect(restoreResult.body.status).toBe("failed")
      expect(restoreResult.body.error).toContain(
        "TEMPLATE_CAPITALIZED ID is required",
      )
    })

    it("should update updatedAt when restoring", async () => {
      const TEMPLATE_CAMELCASEDData = createTEMPLATE_CAPITALIZED(3)
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData,
        businessLogic,
      )
      expect(createResult.status).toBe(201)

      const originalUpdatedAt = createResult.body.data.updatedAt

      // Wait a bit to ensure different timestamp
      await new Promise((resolve) => setTimeout(resolve, 10))

      // Soft delete and restore
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const restoreResult = await implHandleRestoreTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(200)

      const getResult = await implHandleGetTEMPLATE_CAPITALIZED(
        createResult.body.data.id,
        businessLogic,
      )
      expect(getResult.status).toBe(200)
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime(),
      )
    })
  })

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating TEMPLATE_CAMELCASEDs with STRING_FIELD of soft deleted TEMPLATE_CAMELCASEDs", async () => {
      // Create and soft delete a TEMPLATE_CAMELCASEDs
      const TEMPLATE_CAMELCASEDData1 = createTestTEMPLATE_CAPITALIZED()
      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Should be able to create new TEMPLATE_CAMELCASEDs with same STRING_FIELD
      const TEMPLATE_CAMELCASEDData2 = createTestTEMPLATE_CAPITALIZED2()
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData2,
        businessLogic,
      )

      expect(createResult2.status).toBe(201)
      expect(createResult2.body.data.STRING_FIELD).toBe(
        TEMPLATE_CAMELCASEDData2.STRING_FIELD,
      )
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(1)
    })

    it("should prevent creating TEMPLATE_CAMELCASEDs with STRING_FIELD of active TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDData1 = createTestTEMPLATE_CAPITALIZED()
      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const TEMPLATE_CAMELCASEDData2 = createTestTEMPLATE_CAPITALIZED2()
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData2,
        businessLogic,
      )

      expect(createResult2.status).toBe(409)
      expect(createResult2.body.status).toBe("failed")
      expect(createResult2.body.error).toContain(
        "TEMPLATE_CAPITALIZED with the same STRING_FIELD already exists",
      )
    })

    it("should prevent restoring TEMPLATE_CAMELCASEDs if STRING_FIELD is now taken", async () => {
      // Create and soft delete a TEMPLATE_CAMELCASEDs
      const TEMPLATE_CAMELCASEDData1 = createTestTEMPLATE_CAPITALIZED()
      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData1,
        businessLogic,
      )
      expect(createResult1.status).toBe(201)

      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      // Create new TEMPLATE_CAMELCASEDs with same STRING_FIELD
      const TEMPLATE_CAMELCASEDData2 = createTestTEMPLATE_CAPITALIZED2()
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDData2,
        businessLogic,
      )
      expect(createResult2.status).toBe(201)

      // Should not be able to restore the first TEMPLATE_CAMELCASEDs
      const restoreResult = await implHandleRestoreTEMPLATE_CAPITALIZED(
        createResult1.body.data.id,
        businessLogic,
      )
      expect(restoreResult.status).toBe(404)
      expect(restoreResult.body.status).toBe("failed")
    })
  })
})
