// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test"
import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/interface"
import { TEMPLATE_CAPITALIZEDBusinessLogic } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/BusinessLogic"
import { MongoTEMPLATE_CAPITALIZEDRepository } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/MongoRepository"
import { TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper } from "./TestDBRepositoryWrapper"
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver"
import {
  implHandleCreateTEMPLATE_CAPITALIZED,
  implHandleGetTEMPLATE_CAPITALIZED,
  implHandleDeleteTEMPLATE_CAPITALIZED,
} from "@/app/api/v1/TEMPLATE_API_PATHs/impl"
import {
  createTEMPLATE_CAPITALIZED,
  createSimpleTEMPLATE_CAPITALIZEDs,
  createComplexTEMPLATE_CAPITALIZED,
  createMinimalDeleteTEMPLATE_CAPITALIZED,
  createTEMPLATE_CAPITALIZEDsForDeletionTest,
  createRetryDeleteTEMPLATE_CAPITALIZED,
} from "./object_creator"

describe("Delete TEMPLATE_CAPITALIZED API Tests", () => {
  let businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface
  let dbRepository: TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("TEMPLATE_CAPITALIZED")
    await driver.connect()
    const originalDb = new MongoTEMPLATE_CAPITALIZEDRepository(driver)
    dbRepository = new TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper(
      originalDb,
      driver,
    )
    businessLogic = new TEMPLATE_CAPITALIZEDBusinessLogic(dbRepository)
    await dbRepository.clear()
  })

  describe("DELETE /api/v1/TEMPLATE_API_PATHs/:id", () => {
    it("should successfully delete an existing TEMPLATE_CAMELCASED", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZED(5) // John Doe TEMPLATE_CAPITALIZED
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsData,
        businessLogic,
      )
      const TEMPLATE_CAMELCASEDsId = createResult.body.data.id

      const getResult = await implHandleGetTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        businessLogic,
      )
      expect(getResult.status).toBe(200)

      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        businessLogic,
      )

      expect(deleteResult.status).toBe(200)
      expect(deleteResult.body.status).toBe("success")
      expect(deleteResult.body.data.message).toBe(
        "TEMPLATE_CAPITALIZED deleted successfully",
      )

      const getAfterDelete = await implHandleGetTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsId,
        businessLogic,
      )
      expect(getAfterDelete.status).toBe(404)
    })

    it("should verify TEMPLATE_CAMELCASED count decreases after deletion", async () => {
      const TEMPLATE_CAMELCASEDsData = createSimpleTEMPLATE_CAPITALIZEDs()

      const TEMPLATE_CAMELCASEDsIds: string[] = []
      for (const data of TEMPLATE_CAMELCASEDsData) {
        const result = await implHandleCreateTEMPLATE_CAPITALIZED(
          data,
          businessLogic,
        )
        TEMPLATE_CAMELCASEDsIds.push(result.body.data?.id)
      }

      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(3)

      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsIds[1],
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(2)

      const getDeleted = await implHandleGetTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsIds[1],
        businessLogic,
      )
      expect(getDeleted.status).toBe(404)
    })

    it("should fail to delete non-existent TEMPLATE_CAMELCASED", async () => {
      const result = await implHandleDeleteTEMPLATE_CAPITALIZED(
        "507f1f77bcf86cd799439011",
        businessLogic,
      )
      expect(result.status).toBe(404)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("TEMPLATE_CAPITALIZED not found")
    })

    it("should fail with empty TEMPLATE_CAMELCASED ID", async () => {
      const result = await implHandleDeleteTEMPLATE_CAPITALIZED(
        "",
        businessLogic,
      )
      expect(result.status).toBe(400)
      expect(result.body.status).toBe("failed")
      expect(result.body.error).toContain("TEMPLATE_CAPITALIZED ID is required")
    })

    it("should handle deletion with all fields", async () => {
      const TEMPLATE_CAMELCASEDsData = createComplexTEMPLATE_CAPITALIZED()
      const result = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetTEMPLATE_CAPITALIZED(
        id,
        businessLogic,
      )
      expect(getAfter.status).toBe(404)
    })

    it("should handle deletion with minimal fields", async () => {
      const TEMPLATE_CAMELCASEDsData = createMinimalDeleteTEMPLATE_CAPITALIZED()
      const result = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(
        id,
        businessLogic,
      )
      expect(deleteResult.status).toBe(200)

      const getAfter = await implHandleGetTEMPLATE_CAPITALIZED(
        id,
        businessLogic,
      )
      expect(getAfter.status).toBe(404)
    })

    it("should allow deletion of multiple TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDsData = createSimpleTEMPLATE_CAPITALIZEDs()

      const ids: string[] = []
      for (const data of TEMPLATE_CAMELCASEDsData) {
        const res = await implHandleCreateTEMPLATE_CAPITALIZED(
          data,
          businessLogic,
        )
        ids.push(res.body.data.id)
      }

      for (const id of ids) {
        const res = await implHandleDeleteTEMPLATE_CAPITALIZED(
          id,
          businessLogic,
        )
        expect(res.status).toBe(200)
      }

      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(0)
    })

    it("should not affect other TEMPLATE_CAMELCASEDs when deleting one", async () => {
      const TEMPLATE_CAMELCASEDsData =
        createTEMPLATE_CAPITALIZEDsForDeletionTest()

      const ids: string[] = []
      for (const data of TEMPLATE_CAMELCASEDsData) {
        const res = await implHandleCreateTEMPLATE_CAPITALIZED(
          data,
          businessLogic,
        )
        ids.push(res.body.data.id)
      }

      await implHandleDeleteTEMPLATE_CAPITALIZED(ids[1], businessLogic)

      const getDeleted = await implHandleGetTEMPLATE_CAPITALIZED(
        ids[1],
        businessLogic,
      )
      expect(getDeleted.status).toBe(404)

      const first = await implHandleGetTEMPLATE_CAPITALIZED(
        ids[0],
        businessLogic,
      )
      expect(first.status).toBe(200)

      const third = await implHandleGetTEMPLATE_CAPITALIZED(
        ids[2],
        businessLogic,
      )
      expect(third.status).toBe(200)

      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(2)
    })

    it("should handle attempting to delete the same TEMPLATE_CAMELCASED twice", async () => {
      const TEMPLATE_CAMELCASEDsData = createRetryDeleteTEMPLATE_CAPITALIZED()
      const result = await implHandleCreateTEMPLATE_CAPITALIZED(
        TEMPLATE_CAMELCASEDsData,
        businessLogic,
      )
      const id = result.body.data?.id

      const first = await implHandleDeleteTEMPLATE_CAPITALIZED(
        id,
        businessLogic,
      )
      expect(first.status).toBe(200)

      const second = await implHandleDeleteTEMPLATE_CAPITALIZED(
        id,
        businessLogic,
      )
      expect(second.status).toBe(404)
    })
  })
})
