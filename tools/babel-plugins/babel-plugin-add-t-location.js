const path = require("path")

module.exports = function ({ types: t }, options = {}) {
  return {
    visitor: {
      CallExpression(nodePath, state) {
        const filename = state.file.opts.filename || "unknown"

        // Skip node_modules files
        if (filename.includes("node_modules")) {
          return
        }

        // Skip if exclude pattern is provided and matches
        if (
          options.exclude &&
          options.exclude.test &&
          options.exclude.test(filename)
        ) {
          return
        }

        const callee = nodePath.node.callee

        // Check if this is a call to the 't' function
        const isTCall = t.isIdentifier(callee, { name: "t" })
        const argLen = nodePath.node.arguments.length

        // Only transform t() calls with 1 or 2 arguments (key, template?)
        // Skip if already has 3 arguments (already transformed)
        if (isTCall && (argLen === 1 || argLen === 2)) {
          const loc = nodePath.node.loc

          // Get relative path from project root for cleaner output
          const relativePath = path.relative(process.cwd(), filename)

          const fileLiteral = t.stringLiteral(relativePath)
          const lineLiteral = t.numericLiteral(loc ? loc.start.line : 0)

          const metaArg = t.objectExpression([
            t.objectProperty(t.identifier("file"), fileLiteral),
            t.objectProperty(t.identifier("line"), lineLiteral),
          ])

          // Ensure we have exactly 3 arguments: key, template (or undefined), meta
          if (argLen === 1) {
            // t(key) -> t(key, undefined, meta)
            nodePath.node.arguments.push(t.identifier("undefined"))
            nodePath.node.arguments.push(metaArg)
          } else if (argLen === 2) {
            // t(key, template) -> t(key, template, meta)
            nodePath.node.arguments.push(metaArg)
          }
        }
      },
    },
  }
}
