# System Prompts Management

A comprehensive backoffice system for managing CS AI general prompts used in the backend, eliminating the need to change prompts directly in source code.

## 🎯 Features

### Core Functionality

- **CRUD Operations**: Create, read, update, and delete system prompts
- **Query Expansion**: Insert queries to AI based on user queries
- **Industry-Specific Prompts**: Custom prompts tailored for specific industries
- **Content Management**: Manage prompt content, descriptions, and metadata
- **Bulk Operations**: Enable/disable or delete multiple prompts at once

### Prompt Types

- **Query Expansion**: Prompts that expand or enhance user queries before processing
- **Industry Specific**: Prompts tailored for specific industries or domains
- **General Instruction**: General instructions for AI behavior and responses
- **Context Enhancement**: Prompts that add context to improve response quality
- **Response Formatting**: Prompts that control response format and structure
- **Safety Filter**: Prompts for content safety and moderation
- **Custom**: Custom prompts for specific use cases

### Advanced Features

- **Search & Filtering**: Full-text search with advanced filtering options
- **Tagging System**: Organize prompts with custom tags
- **Priority System**: Set priority levels (0-100) for prompt execution order
- **Status Management**: Enable/disable prompts without deletion
- **Statistics Dashboard**: View usage statistics and prompt distribution
- **Duplicate Functionality**: Clone existing prompts for quick creation

## 🏗️ Architecture

### Repository Pattern

```
SystemPromptsRepository (Interface)
├── MongoDBSystemPromptsRepository (Implementation)
└── Future: RedisSystemPromptsRepository (Caching)
```

### Service Layer

```
SystemPromptsService
├── Validation Logic
├── Business Rules
└── Error Handling
```

### Database Schema

```typescript
interface SystemPrompt {
  id: string
  title: string
  description: string
  query: string
  content: string
  type: SystemPromptType
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  createdBy?: string
  updatedBy?: string
  tags?: string[]
  priority?: number
}
```

## 🚀 API Endpoints

### Main CRUD Operations

- `GET /api/system-prompts` - List prompts with filtering
- `POST /api/system-prompts` - Create new prompt
- `GET /api/system-prompts/{id}` - Get single prompt
- `PUT /api/system-prompts/{id}` - Update prompt
- `DELETE /api/system-prompts/{id}` - Delete prompt

### Additional Operations

- `POST /api/system-prompts/{id}/duplicate` - Duplicate prompt
- `POST /api/system-prompts/bulk/toggle` - Bulk enable/disable
- `POST /api/system-prompts/bulk/delete` - Bulk delete
- `GET /api/system-prompts/stats` - Get statistics
- `GET /api/system-prompts/search` - Search prompts

## 🎨 UI Components

### Main Components

- **SystemPromptsList**: Table view with sorting, filtering, and bulk actions
- **SystemPromptsForm**: Modal form for creating/editing prompts
- **SystemPromptsStats**: Dashboard showing statistics and metrics
- **SystemPromptsFilters**: Advanced filtering interface

### Features

- **Responsive Design**: Works on desktop and mobile devices
- **Modern UI**: Clean, professional interface using Tailwind CSS
- **Real-time Updates**: Immediate feedback for all operations
- **Accessibility**: Keyboard navigation and screen reader support

## 📊 Usage Examples

### Creating a Query Expansion Prompt

```typescript
const queryExpansionPrompt = {
  title: "Customer Service Query Enhancement",
  description: "Enhances customer service queries with context",
  query: "customer service OR support OR help",
  content:
    "When a user asks about customer service, also consider: product issues, billing questions, account problems, and technical support.",
  type: SystemPromptType.QUERY_EXPANSION,
  isActive: true,
  tags: ["customer-service", "support"],
  priority: 80,
}
```

### Industry-Specific Prompt

```typescript
const healthcarePrompt = {
  title: "Healthcare Industry Context",
  description: "Adds healthcare-specific context to responses",
  query: "healthcare OR medical OR health",
  content:
    "When discussing healthcare topics, always emphasize: 1) Consult healthcare professionals, 2) This is not medical advice, 3) Emergency situations require immediate medical attention.",
  type: SystemPromptType.INDUSTRY_SPECIFIC,
  isActive: true,
  tags: ["healthcare", "medical", "safety"],
  priority: 95,
}
```

## 🔧 Configuration

### Environment Variables

```env
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/cs-ai-prompts

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379

# Application Settings
NODE_ENV=development
```

### Database Indexes

The system automatically creates the following indexes for optimal performance:

- `title` (unique)
- `type`
- `isActive`
- `tags`
- `createdAt`
- `updatedAt`
- `priority`
- Text index on `title`, `description`, `content`

## 🚦 Getting Started

### 1. Installation

```bash
cd subprojects/system_prompts
npm install
```

### 2. Database Setup

```bash
# Initialize database (creates indexes)
npm run db:init
```

### 3. Development

```bash
# Start development server
npm run dev
```

### 4. Access the Application

- **System Prompts Management**: http://localhost:5173/
- **Direct Access**: http://localhost:5173/system-prompts

## 🔍 Search and Filtering

### Search Capabilities

- **Full-text search** across title, description, and content
- **Type filtering** by prompt type
- **Status filtering** (active/inactive)
- **Tag filtering** with multiple tag support
- **Date range filtering** (coming soon)

### Sorting Options

- Last Updated (default)
- Created Date
- Title (alphabetical)
- Priority (highest first)

## 📈 Statistics and Analytics

### Available Metrics

- Total prompts count
- Active vs inactive prompts
- Distribution by prompt type
- Usage statistics (coming soon)
- Performance metrics (coming soon)

## 🔒 Security Considerations

### Data Validation

- Input sanitization for all fields
- Length limits on text fields
- Type validation for enums
- Priority range validation (0-100)

### Access Control

- Future: Role-based access control
- Future: Audit logging for all changes
- Future: API rate limiting

## 🚀 Future Enhancements

### Planned Features

- **Version Control**: Track prompt changes over time
- **A/B Testing**: Test different prompt variations
- **Analytics**: Detailed usage and performance metrics
- **Import/Export**: Bulk import/export functionality
- **Templates**: Pre-built prompt templates
- **Collaboration**: Multi-user editing with conflict resolution
- **API Integration**: Direct integration with AI backend services

### Technical Improvements

- **Caching Layer**: Redis integration for improved performance
- **Real-time Updates**: WebSocket support for live collaboration
- **Advanced Search**: Elasticsearch integration
- **Backup System**: Automated backup and restore functionality

## 🤝 Contributing

### Development Guidelines

1. Follow TypeScript best practices
2. Maintain test coverage above 80%
3. Use semantic commit messages
4. Update documentation for new features

### Code Structure

```
src/
├── lib/
│   ├── types/system-prompts.ts          # Type definitions
│   ├── repositories/                    # Data access layer
│   ├── services/                        # Business logic layer
│   └── components/                      # UI components
├── routes/
│   ├── api/system-prompts/             # API endpoints
│   └── system-prompts/                 # UI pages
└── app.css                             # Global styles
```

This system provides a robust, scalable solution for managing AI prompts with a modern, user-friendly interface that can be extended for future requirements.
