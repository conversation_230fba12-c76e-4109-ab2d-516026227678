<script lang="ts">
  import { onMount } from "svelte"
  import { goto } from "$app/navigation"

  onMount(() => {
    // Redirect to system prompts page
    goto("/system-prompts")
  })
</script>

<div class="redirect-page">
  <div class="redirect-content">
    <div class="spinner"></div>
    <p>Redirecting to System Prompts Management...</p>
  </div>
</div>

<style>
  .redirect-page {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9fafb;
  }

  .redirect-content {
    text-align: center;
    color: #6b7280;
  }

  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .redirect-content p {
    margin: 0;
    font-size: 14px;
  }
</style>
