<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { 
    SystemPrompt, 
    SystemPromptCreateInput, 
    SystemPromptUpdateInput,
    SystemPromptType 
  } from '../types/system-prompts'
  import { 
    SystemPromptType as PromptType, 
    SYSTEM_PROMPT_TYPE_LABELS,
    SYSTEM_PROMPT_TYPE_DESCRIPTIONS 
  } from '../types/system-prompts'
  import { X, Save, Eye, EyeOff } from 'lucide-svelte'

  export let prompt: SystemPrompt | null = null

  const dispatch = createEventDispatcher()

  let formData = {
    title: '',
    description: '',
    query: '',
    content: '',
    type: PromptType.GENERAL_INSTRUCTION as SystemPromptType,
    isActive: true,
    tags: [] as string[],
    priority: 0
  }

  let errors: Record<string, string> = {}
  let isSubmitting = false
  let tagInput = ''
  let showPreview = false

  // Initialize form data when prompt changes
  $: if (prompt) {
    formData = {
      title: prompt.title,
      description: prompt.description,
      query: prompt.query,
      content: prompt.content,
      type: prompt.type,
      isActive: prompt.isActive,
      tags: [...(prompt.tags || [])],
      priority: prompt.priority || 0
    }
  } else {
    formData = {
      title: '',
      description: '',
      query: '',
      content: '',
      type: PromptType.GENERAL_INSTRUCTION,
      isActive: true,
      tags: [],
      priority: 0
    }
  }

  function validateForm(): boolean {
    errors = {}

    if (!formData.title.trim()) {
      errors.title = 'Title is required'
    } else if (formData.title.length > 200) {
      errors.title = 'Title must be less than 200 characters'
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required'
    } else if (formData.description.length > 1000) {
      errors.description = 'Description must be less than 1000 characters'
    }

    if (!formData.query.trim()) {
      errors.query = 'Query is required'
    }

    if (!formData.content.trim()) {
      errors.content = 'Content is required'
    }

    if (formData.priority < 0 || formData.priority > 100) {
      errors.priority = 'Priority must be between 0 and 100'
    }

    return Object.keys(errors).length === 0
  }

  async function handleSubmit(): Promise<void> {
    if (!validateForm()) return

    isSubmitting = true

    try {
      if (prompt) {
        // Update existing prompt
        const updateData: SystemPromptUpdateInput = {
          title: formData.title.trim(),
          description: formData.description.trim(),
          query: formData.query.trim(),
          content: formData.content.trim(),
          type: formData.type,
          isActive: formData.isActive,
          tags: formData.tags,
          priority: formData.priority
        }
        dispatch('update', { id: prompt.id, data: updateData })
      } else {
        // Create new prompt
        const createData: SystemPromptCreateInput = {
          title: formData.title.trim(),
          description: formData.description.trim(),
          query: formData.query.trim(),
          content: formData.content.trim(),
          type: formData.type,
          isActive: formData.isActive,
          tags: formData.tags,
          priority: formData.priority
        }
        dispatch('create', createData)
      }
    } finally {
      isSubmitting = false
    }
  }

  function handleCancel(): void {
    dispatch('cancel')
  }

  function addTag(): void {
    const tag = tagInput.trim()
    if (tag && !formData.tags.includes(tag)) {
      formData.tags = [...formData.tags, tag]
      tagInput = ''
    }
  }

  function removeTag(tagToRemove: string): void {
    formData.tags = formData.tags.filter(tag => tag !== tagToRemove)
  }

  function handleTagKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault()
      addTag()
    }
  }

  function handleClickOutside(event: MouseEvent): void {
    const target = event.target as HTMLElement
    if (!target.closest('.modal-content')) {
      handleCancel()
    }
  }
</script>

<div class="modal-overlay" on:click={handleClickOutside}>
  <div class="modal-content" on:click|stopPropagation>
    <div class="modal-header">
      <h2>{prompt ? 'Edit System Prompt' : 'Create New System Prompt'}</h2>
      <button class="close-btn" on:click={handleCancel}>
        <X size={20} />
      </button>
    </div>

    <form class="prompt-form" on:submit|preventDefault={handleSubmit}>
      <div class="form-grid">
        <!-- Title -->
        <div class="form-group">
          <label for="title">Title *</label>
          <input
            id="title"
            type="text"
            bind:value={formData.title}
            placeholder="Enter prompt title..."
            class:error={errors.title}
            maxlength="200"
          />
          {#if errors.title}
            <span class="error-text">{errors.title}</span>
          {/if}
        </div>

        <!-- Type -->
        <div class="form-group">
          <label for="type">Type *</label>
          <select id="type" bind:value={formData.type} class:error={errors.type}>
            {#each Object.values(PromptType) as type}
              <option value={type}>{SYSTEM_PROMPT_TYPE_LABELS[type]}</option>
            {/each}
          </select>
          {#if formData.type}
            <p class="type-description">{SYSTEM_PROMPT_TYPE_DESCRIPTIONS[formData.type]}</p>
          {/if}
        </div>

        <!-- Description -->
        <div class="form-group full-width">
          <label for="description">Description *</label>
          <textarea
            id="description"
            bind:value={formData.description}
            placeholder="Describe what this prompt does..."
            rows="3"
            class:error={errors.description}
            maxlength="1000"
          ></textarea>
          {#if errors.description}
            <span class="error-text">{errors.description}</span>
          {/if}
        </div>

        <!-- Query -->
        <div class="form-group full-width">
          <label for="query">Query Pattern *</label>
          <textarea
            id="query"
            bind:value={formData.query}
            placeholder="Enter the query pattern or trigger..."
            rows="2"
            class:error={errors.query}
          ></textarea>
          {#if errors.query}
            <span class="error-text">{errors.query}</span>
          {/if}
          <p class="field-help">Define when this prompt should be triggered</p>
        </div>

        <!-- Content -->
        <div class="form-group full-width">
          <div class="content-header">
            <label for="content">Prompt Content *</label>
            <button
              type="button"
              class="preview-btn"
              on:click={() => showPreview = !showPreview}
            >
              {#if showPreview}
                <EyeOff size={16} />
                Edit
              {:else}
                <Eye size={16} />
                Preview
              {/if}
            </button>
          </div>
          
          {#if showPreview}
            <div class="content-preview">
              <pre>{formData.content}</pre>
            </div>
          {:else}
            <textarea
              id="content"
              bind:value={formData.content}
              placeholder="Enter the prompt content..."
              rows="8"
              class:error={errors.content}
            ></textarea>
          {/if}
          
          {#if errors.content}
            <span class="error-text">{errors.content}</span>
          {/if}
          <p class="field-help">The actual prompt text that will be used by the AI</p>
        </div>

        <!-- Tags -->
        <div class="form-group">
          <label for="tags">Tags</label>
          <div class="tags-input">
            <input
              type="text"
              bind:value={tagInput}
              placeholder="Add tag..."
              on:keydown={handleTagKeydown}
            />
            <button type="button" on:click={addTag}>Add</button>
          </div>
          {#if formData.tags.length > 0}
            <div class="tags-list">
              {#each formData.tags as tag}
                <span class="tag">
                  {tag}
                  <button type="button" on:click={() => removeTag(tag)}>×</button>
                </span>
              {/each}
            </div>
          {/if}
        </div>

        <!-- Priority -->
        <div class="form-group">
          <label for="priority">Priority</label>
          <input
            id="priority"
            type="number"
            bind:value={formData.priority}
            min="0"
            max="100"
            class:error={errors.priority}
          />
          {#if errors.priority}
            <span class="error-text">{errors.priority}</span>
          {/if}
          <p class="field-help">0-100 (higher = more priority)</p>
        </div>

        <!-- Status -->
        <div class="form-group full-width">
          <label class="checkbox-label">
            <input
              type="checkbox"
              bind:checked={formData.isActive}
            />
            <span class="checkbox-text">Active</span>
          </label>
          <p class="field-help">Only active prompts will be used by the AI</p>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-secondary" on:click={handleCancel}>
          Cancel
        </button>
        <button 
          type="submit" 
          class="btn btn-primary"
          disabled={isSubmitting}
        >
          <Save size={16} />
          {isSubmitting ? 'Saving...' : (prompt ? 'Update' : 'Create')}
        </button>
      </div>
    </form>
  </div>
</div>

<style>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
  }

  .modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
  }

  .modal-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }

  .close-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .close-btn:hover {
    background: #f3f4f6;
    color: #374151;
  }

  .prompt-form {
    padding: 0 24px 24px 24px;
    overflow-y: auto;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .form-group.full-width {
    grid-column: 1 / -1;
  }

  .form-group label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-group input.error,
  .form-group select.error,
  .form-group textarea.error {
    border-color: #dc2626;
  }

  .error-text {
    color: #dc2626;
    font-size: 12px;
  }

  .field-help {
    color: #6b7280;
    font-size: 12px;
    margin: 0;
  }

  .type-description {
    color: #6b7280;
    font-size: 12px;
    margin: 0;
    font-style: italic;
  }

  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .preview-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .preview-btn:hover {
    background: #e5e7eb;
  }

  .content-preview {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 12px;
    background: #f9fafb;
    min-height: 200px;
  }

  .content-preview pre {
    margin: 0;
    white-space: pre-wrap;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
  }

  .tags-input {
    display: flex;
    gap: 8px;
  }

  .tags-input input {
    flex: 1;
  }

  .tags-input button {
    padding: 8px 12px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
  }

  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
  }

  .tag {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #e5e7eb;
    color: #374151;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .tag button {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
  }

  .tag button:hover {
    background: #d1d5db;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }

  .checkbox-text {
    font-size: 14px;
    color: #374151;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 32px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
  }

  .btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: 1px solid transparent;
    transition: all 0.2s;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background: #3b82f6;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: #2563eb;
  }

  .btn-secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;
  }

  .btn-secondary:hover {
    background: #f9fafb;
  }
</style>
