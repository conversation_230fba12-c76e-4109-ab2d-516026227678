<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import type {
    SystemPrompt,
    SystemPromptFilter,
  } from "../types/system-prompts"
  import { SYSTEM_PROMPT_TYPE_LABELS } from "../types/system-prompts"
  import { Edit, Trash2, Co<PERSON>, MoreVertical, Eye, EyeOff } from "lucide-svelte"

  export let systemPrompts: SystemPrompt[] = []
  export let selectedPrompts: string[] = []
  export let pagination: { total: number; hasMore: boolean }
  export let filter: SystemPromptFilter

  const dispatch = createEventDispatcher()

  function handleSelectAll(event: Event): void {
    const target = event.target as HTMLInputElement
    if (target.checked) {
      dispatch(
        "selectionChange",
        systemPrompts.map((p) => p.id),
      )
    } else {
      dispatch("selectionChange", [])
    }
  }

  function handleSelectPrompt(promptId: string, event: Event): void {
    const target = event.target as HTMLInputElement
    let newSelection = [...selectedPrompts]

    if (target.checked) {
      newSelection.push(promptId)
    } else {
      newSelection = newSelection.filter((id) => id !== promptId)
    }

    dispatch("selectionChange", newSelection)
  }

  function handleEdit(prompt: SystemPrompt): void {
    dispatch("edit", prompt)
  }

  function handleDelete(prompt: SystemPrompt): void {
    if (confirm(`Are you sure you want to delete "${prompt.title}"?`)) {
      dispatch("delete", { id: prompt.id })
    }
  }

  function handleDuplicate(prompt: SystemPrompt): void {
    const newTitle = window.prompt(`Copy of ${prompt.title}`)
    if (newTitle) {
      dispatch("duplicate", { id: prompt.id, newTitle })
    }
  }

  function handlePrevPage(): void {
    const newOffset = Math.max(0, (filter.offset || 0) - (filter.limit || 20))
    dispatch("pageChange", newOffset)
  }

  function handleNextPage(): void {
    const newOffset = (filter.offset || 0) + (filter.limit || 20)
    dispatch("pageChange", newOffset)
  }

  function formatDate(date: Date | string): string {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  function truncateText(text: string, maxLength: number = 100): string {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  $: allSelected =
    systemPrompts.length > 0 && selectedPrompts.length === systemPrompts.length
  $: someSelected =
    selectedPrompts.length > 0 && selectedPrompts.length < systemPrompts.length
  $: currentPage = Math.floor((filter.offset || 0) / (filter.limit || 20)) + 1
  $: totalPages = Math.ceil(pagination.total / (filter.limit || 20))
</script>

<div class="system-prompts-list">
  {#if systemPrompts.length === 0}
    <div class="empty-state">
      <div class="empty-icon">📝</div>
      <h3>No system prompts found</h3>
      <p>
        Create your first system prompt to get started with AI customization.
      </p>
    </div>
  {:else}
    <div class="table-container">
      <table class="prompts-table">
        <thead>
          <tr>
            <th class="checkbox-col">
              <input
                type="checkbox"
                checked={allSelected}
                indeterminate={someSelected}
                on:change={handleSelectAll}
              />
            </th>
            <th class="title-col">Title & Description</th>
            <th class="type-col">Type</th>
            <th class="status-col">Status</th>
            <th class="updated-col">Updated</th>
            <th class="actions-col">Actions</th>
          </tr>
        </thead>
        <tbody>
          {#each systemPrompts as prompt (prompt.id)}
            <tr
              class="prompt-row"
              class:selected={selectedPrompts.includes(prompt.id)}
            >
              <td class="checkbox-col">
                <input
                  type="checkbox"
                  checked={selectedPrompts.includes(prompt.id)}
                  on:change={(e) => handleSelectPrompt(prompt.id, e)}
                />
              </td>

              <td class="title-col">
                <div class="prompt-info">
                  <h4 class="prompt-title">{prompt.title}</h4>
                  <p class="prompt-description">
                    {truncateText(prompt.description)}
                  </p>
                  {#if prompt.tags && prompt.tags.length > 0}
                    <div class="tags">
                      {#each prompt.tags.slice(0, 3) as tag}
                        <span class="tag">{tag}</span>
                      {/each}
                      {#if prompt.tags.length > 3}
                        <span class="tag-more">+{prompt.tags.length - 3}</span>
                      {/if}
                    </div>
                  {/if}
                </div>
              </td>

              <td class="type-col">
                <span class="type-badge type-{prompt.type}">
                  {SYSTEM_PROMPT_TYPE_LABELS[prompt.type]}
                </span>
              </td>

              <td class="status-col">
                <div class="status-indicator">
                  {#if prompt.isActive}
                    <Eye size={16} class="status-icon active" />
                    <span class="status-text active">Active</span>
                  {:else}
                    <EyeOff size={16} class="status-icon inactive" />
                    <span class="status-text inactive">Inactive</span>
                  {/if}
                </div>
              </td>

              <td class="updated-col">
                <time class="updated-time">
                  {formatDate(prompt.updatedAt)}
                </time>
                {#if prompt.priority && prompt.priority > 0}
                  <div class="priority">Priority: {prompt.priority}</div>
                {/if}
              </td>

              <td class="actions-col">
                <div class="actions-menu">
                  <button
                    class="action-btn"
                    on:click={() => handleEdit(prompt)}
                    title="Edit prompt"
                  >
                    <Edit size={16} />
                  </button>

                  <button
                    class="action-btn"
                    on:click={() => handleDuplicate(prompt)}
                    title="Duplicate prompt"
                  >
                    <Copy size={16} />
                  </button>

                  <button
                    class="action-btn danger"
                    on:click={() => handleDelete(prompt)}
                    title="Delete prompt"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {#if totalPages > 1}
      <div class="pagination">
        <div class="pagination-info">
          Showing {(filter.offset || 0) + 1} to {Math.min(
            (filter.offset || 0) + (filter.limit || 20),
            pagination.total,
          )} of {pagination.total} prompts
        </div>

        <div class="pagination-controls">
          <button
            class="pagination-btn"
            disabled={currentPage === 1}
            on:click={handlePrevPage}
          >
            Previous
          </button>

          <span class="page-info">
            Page {currentPage} of {totalPages}
          </span>

          <button
            class="pagination-btn"
            disabled={!pagination.hasMore}
            on:click={handleNextPage}
          >
            Next
          </button>
        </div>
      </div>
    {/if}
  {/if}
</div>

<style>
  .system-prompts-list {
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
  }

  .empty-state {
    text-align: center;
    padding: 64px 32px;
    color: #6b7280;
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-state h3 {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
  }

  .empty-state p {
    margin: 0;
  }

  .table-container {
    overflow-x: auto;
  }

  .prompts-table {
    width: 100%;
    border-collapse: collapse;
  }

  .prompts-table th {
    background: #f9fafb;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    font-size: 14px;
  }

  .prompts-table td {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: top;
  }

  .checkbox-col {
    width: 48px;
  }

  .title-col {
    min-width: 300px;
  }

  .type-col {
    width: 150px;
  }

  .status-col {
    width: 120px;
  }

  .updated-col {
    width: 160px;
  }

  .actions-col {
    width: 120px;
  }

  .prompt-row:hover {
    background: #f9fafb;
  }

  .prompt-row.selected {
    background: #eff6ff;
  }

  .prompt-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .prompt-title {
    font-size: 14px;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }

  .prompt-description {
    font-size: 13px;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
  }

  .tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-top: 4px;
  }

  .tag {
    background: #e5e7eb;
    color: #374151;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
  }

  .tag-more {
    background: #d1d5db;
    color: #6b7280;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
  }

  .type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .type-query_expansion {
    background: #dbeafe;
    color: #1e40af;
  }

  .type-industry_specific {
    background: #d1fae5;
    color: #065f46;
  }

  .type-general_instruction {
    background: #fef3c7;
    color: #92400e;
  }

  .type-context_enhancement {
    background: #e0e7ff;
    color: #3730a3;
  }

  .type-response_formatting {
    background: #fce7f3;
    color: #be185d;
  }

  .type-safety_filter {
    background: #fee2e2;
    color: #dc2626;
  }

  .type-custom {
    background: #f3f4f6;
    color: #374151;
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .status-icon.active {
    color: #059669;
  }

  .status-icon.inactive {
    color: #6b7280;
  }

  .status-text {
    font-size: 13px;
    font-weight: 500;
  }

  .status-text.active {
    color: #059669;
  }

  .status-text.inactive {
    color: #6b7280;
  }

  .updated-time {
    font-size: 13px;
    color: #6b7280;
  }

  .priority {
    font-size: 11px;
    color: #9ca3af;
    margin-top: 2px;
  }

  .actions-menu {
    display: flex;
    gap: 4px;
  }

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 4px;
    cursor: pointer;
    color: #6b7280;
    transition: all 0.2s;
  }

  .action-btn:hover {
    background: #f3f4f6;
    color: #374151;
  }

  .action-btn.danger:hover {
    background: #fee2e2;
    color: #dc2626;
  }

  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
  }

  .pagination-info {
    font-size: 14px;
    color: #6b7280;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .pagination-btn {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .pagination-btn:hover:not(:disabled) {
    background: #f9fafb;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .page-info {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
  }
</style>
