<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { SystemPromptFilter, SystemPromptType } from '../types/system-prompts'
  import { SystemPromptType as PromptType, SYSTEM_PROMPT_TYPE_LABELS } from '../types/system-prompts'
  import { Filter, X, RotateCcw } from 'lucide-svelte'

  export let filter: SystemPromptFilter

  const dispatch = createEventDispatcher()

  let localFilter = { ...filter }
  let tagInput = ''

  // Reactive updates
  $: {
    localFilter = { ...filter }
  }

  function applyFilters(): void {
    dispatch('filterChange', localFilter)
  }

  function resetFilters(): void {
    localFilter = {
      limit: 20,
      offset: 0,
      sortBy: 'updatedAt',
      sortOrder: 'desc'
    }
    dispatch('filterChange', localFilter)
  }

  function addTag(): void {
    const tag = tagInput.trim()
    if (tag && (!localFilter.tags || !localFilter.tags.includes(tag))) {
      localFilter.tags = [...(localFilter.tags || []), tag]
      tagInput = ''
      applyFilters()
    }
  }

  function removeTag(tagToRemove: string): void {
    if (localFilter.tags) {
      localFilter.tags = localFilter.tags.filter(tag => tag !== tagToRemove)
      if (localFilter.tags.length === 0) {
        delete localFilter.tags
      }
      applyFilters()
    }
  }

  function handleTagKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault()
      addTag()
    }
  }

  function handleTypeChange(event: Event): void {
    const target = event.target as HTMLSelectElement
    if (target.value) {
      localFilter.type = target.value as SystemPromptType
    } else {
      delete localFilter.type
    }
    applyFilters()
  }

  function handleStatusChange(event: Event): void {
    const target = event.target as HTMLSelectElement
    if (target.value === '') {
      delete localFilter.isActive
    } else {
      localFilter.isActive = target.value === 'true'
    }
    applyFilters()
  }

  function handleSortChange(): void {
    applyFilters()
  }

  $: hasActiveFilters = !!(
    localFilter.type ||
    localFilter.isActive !== undefined ||
    (localFilter.tags && localFilter.tags.length > 0) ||
    localFilter.search
  )
</script>

<div class="filters-container">
  <div class="filters-header">
    <div class="filters-title">
      <Filter size={16} />
      <span>Filters</span>
      {#if hasActiveFilters}
        <span class="active-indicator">•</span>
      {/if}
    </div>
    
    {#if hasActiveFilters}
      <button class="reset-btn" on:click={resetFilters}>
        <RotateCcw size={14} />
        Reset
      </button>
    {/if}
  </div>

  <div class="filters-grid">
    <!-- Type Filter -->
    <div class="filter-group">
      <label for="type-filter">Type</label>
      <select 
        id="type-filter" 
        value={localFilter.type || ''} 
        on:change={handleTypeChange}
      >
        <option value="">All Types</option>
        {#each Object.values(PromptType) as type}
          <option value={type}>{SYSTEM_PROMPT_TYPE_LABELS[type]}</option>
        {/each}
      </select>
    </div>

    <!-- Status Filter -->
    <div class="filter-group">
      <label for="status-filter">Status</label>
      <select 
        id="status-filter" 
        value={localFilter.isActive === undefined ? '' : localFilter.isActive.toString()} 
        on:change={handleStatusChange}
      >
        <option value="">All Status</option>
        <option value="true">Active</option>
        <option value="false">Inactive</option>
      </select>
    </div>

    <!-- Sort By -->
    <div class="filter-group">
      <label for="sort-by">Sort By</label>
      <select 
        id="sort-by" 
        bind:value={localFilter.sortBy} 
        on:change={handleSortChange}
      >
        <option value="updatedAt">Last Updated</option>
        <option value="createdAt">Created Date</option>
        <option value="title">Title</option>
        <option value="priority">Priority</option>
      </select>
    </div>

    <!-- Sort Order -->
    <div class="filter-group">
      <label for="sort-order">Order</label>
      <select 
        id="sort-order" 
        bind:value={localFilter.sortOrder} 
        on:change={handleSortChange}
      >
        <option value="desc">Descending</option>
        <option value="asc">Ascending</option>
      </select>
    </div>

    <!-- Tags Filter -->
    <div class="filter-group tags-filter">
      <label for="tags-filter">Tags</label>
      <div class="tags-input">
        <input
          id="tags-filter"
          type="text"
          bind:value={tagInput}
          placeholder="Add tag filter..."
          on:keydown={handleTagKeydown}
        />
        <button type="button" on:click={addTag} disabled={!tagInput.trim()}>
          Add
        </button>
      </div>
      
      {#if localFilter.tags && localFilter.tags.length > 0}
        <div class="active-tags">
          {#each localFilter.tags as tag}
            <span class="tag">
              {tag}
              <button type="button" on:click={() => removeTag(tag)}>
                <X size={12} />
              </button>
            </span>
          {/each}
        </div>
      {/if}
    </div>

    <!-- Results Per Page -->
    <div class="filter-group">
      <label for="limit-filter">Per Page</label>
      <select 
        id="limit-filter" 
        bind:value={localFilter.limit} 
        on:change={applyFilters}
      >
        <option value={10}>10</option>
        <option value={20}>20</option>
        <option value={50}>50</option>
        <option value={100}>100</option>
      </select>
    </div>
  </div>
</div>

<style>
  .filters-container {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
  }

  .filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .filters-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }

  .active-indicator {
    color: #3b82f6;
    font-size: 16px;
    line-height: 1;
  }

  .reset-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s;
  }

  .reset-btn:hover {
    background: #f3f4f6;
    color: #374151;
  }

  .filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .filter-group.tags-filter {
    grid-column: 1 / -1;
  }

  .filter-group label {
    font-size: 13px;
    font-weight: 500;
    color: #374151;
  }

  .filter-group select,
  .filter-group input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    transition: border-color 0.2s;
  }

  .filter-group select:focus,
  .filter-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .tags-input {
    display: flex;
    gap: 8px;
  }

  .tags-input input {
    flex: 1;
  }

  .tags-input button {
    padding: 8px 12px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s;
  }

  .tags-input button:hover:not(:disabled) {
    background: #2563eb;
  }

  .tags-input button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .active-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
  }

  .tag {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #e5e7eb;
    color: #374151;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  .tag button {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    transition: all 0.2s;
  }

  .tag button:hover {
    background: #d1d5db;
    color: #374151;
  }

  @media (max-width: 768px) {
    .filters-grid {
      grid-template-columns: 1fr;
    }
    
    .filter-group.tags-filter {
      grid-column: 1;
    }
    
    .filters-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
</style>
