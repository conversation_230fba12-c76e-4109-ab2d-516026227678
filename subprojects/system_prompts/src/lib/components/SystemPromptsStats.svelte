<script lang="ts">
  import type { SystemPromptStats } from '../types/system-prompts'
  import { SYSTEM_PROMPT_TYPE_LABELS } from '../types/system-prompts'
  import { BarChart3, Activity, Eye, EyeOff } from 'lucide-svelte'

  export let stats: SystemPromptStats

  $: activePercentage = stats.total > 0 ? Math.round((stats.active / stats.total) * 100) : 0
  $: inactivePercentage = 100 - activePercentage
</script>

<div class="stats-container">
  <div class="stats-grid">
    <!-- Total Prompts -->
    <div class="stat-card primary">
      <div class="stat-icon">
        <BarChart3 size={24} />
      </div>
      <div class="stat-content">
        <div class="stat-value">{stats.total}</div>
        <div class="stat-label">Total Prompts</div>
      </div>
    </div>

    <!-- Active Prompts -->
    <div class="stat-card success">
      <div class="stat-icon">
        <Eye size={24} />
      </div>
      <div class="stat-content">
        <div class="stat-value">{stats.active}</div>
        <div class="stat-label">Active</div>
        <div class="stat-percentage">{activePercentage}%</div>
      </div>
    </div>

    <!-- Inactive Prompts -->
    <div class="stat-card warning">
      <div class="stat-icon">
        <EyeOff size={24} />
      </div>
      <div class="stat-content">
        <div class="stat-value">{stats.inactive}</div>
        <div class="stat-label">Inactive</div>
        <div class="stat-percentage">{inactivePercentage}%</div>
      </div>
    </div>

    <!-- Activity Indicator -->
    <div class="stat-card info">
      <div class="stat-icon">
        <Activity size={24} />
      </div>
      <div class="stat-content">
        <div class="stat-value">
          {stats.active > 0 ? 'Active' : 'Inactive'}
        </div>
        <div class="stat-label">System Status</div>
      </div>
    </div>
  </div>

  <!-- Type Breakdown -->
  {#if Object.keys(stats.byType).length > 0}
    <div class="type-breakdown">
      <h3 class="breakdown-title">Prompts by Type</h3>
      <div class="type-grid">
        {#each Object.entries(stats.byType) as [type, count]}
          {#if count > 0}
            <div class="type-item">
              <div class="type-info">
                <span class="type-name">{SYSTEM_PROMPT_TYPE_LABELS[type]}</span>
                <span class="type-count">{count}</span>
              </div>
              <div class="type-bar">
                <div 
                  class="type-bar-fill type-{type}"
                  style="width: {stats.total > 0 ? (count / stats.total) * 100 : 0}%"
                ></div>
              </div>
            </div>
          {/if}
        {/each}
      </div>
    </div>
  {/if}
</div>

<style>
  .stats-container {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid;
    transition: transform 0.2s;
  }

  .stat-card:hover {
    transform: translateY(-2px);
  }

  .stat-card.primary {
    background: #eff6ff;
    border-color: #bfdbfe;
  }

  .stat-card.success {
    background: #f0fdf4;
    border-color: #bbf7d0;
  }

  .stat-card.warning {
    background: #fffbeb;
    border-color: #fed7aa;
  }

  .stat-card.info {
    background: #f0f9ff;
    border-color: #bae6fd;
  }

  .stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    flex-shrink: 0;
  }

  .primary .stat-icon {
    background: #dbeafe;
    color: #1e40af;
  }

  .success .stat-icon {
    background: #dcfce7;
    color: #166534;
  }

  .warning .stat-icon {
    background: #fef3c7;
    color: #d97706;
  }

  .info .stat-icon {
    background: #e0f2fe;
    color: #0369a1;
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    line-height: 1;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
  }

  .stat-percentage {
    font-size: 12px;
    color: #9ca3af;
    margin-top: 2px;
  }

  .type-breakdown {
    border-top: 1px solid #e5e7eb;
    padding-top: 24px;
  }

  .breakdown-title {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 16px 0;
  }

  .type-grid {
    display: grid;
    gap: 12px;
  }

  .type-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .type-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .type-name {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
  }

  .type-count {
    font-size: 14px;
    color: #6b7280;
    font-weight: 600;
  }

  .type-bar {
    height: 6px;
    background: #f3f4f6;
    border-radius: 3px;
    overflow: hidden;
  }

  .type-bar-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
  }

  .type-bar-fill.type-query_expansion {
    background: #3b82f6;
  }

  .type-bar-fill.type-industry_specific {
    background: #10b981;
  }

  .type-bar-fill.type-general_instruction {
    background: #f59e0b;
  }

  .type-bar-fill.type-context_enhancement {
    background: #8b5cf6;
  }

  .type-bar-fill.type-response_formatting {
    background: #ec4899;
  }

  .type-bar-fill.type-safety_filter {
    background: #ef4444;
  }

  .type-bar-fill.type-custom {
    background: #6b7280;
  }

  @media (max-width: 768px) {
    .stats-grid {
      grid-template-columns: 1fr;
    }
    
    .stat-card {
      padding: 16px;
    }
    
    .stat-icon {
      width: 40px;
      height: 40px;
    }
    
    .stat-value {
      font-size: 20px;
    }
  }
</style>
