import { MongoClient, Db, Collection } from "mongodb"
import { getServerEnv, logEnvVars } from "./env"

// Get environment configuration
const env = getServerEnv()
const MONGODB_URI = env.MONGODB_URI
const DATABASE_NAME = env.MONGODB_DATABASE

// Log environment variables for debugging
logEnvVars()

class MongoDB {
  private client: MongoClient | null = null
  readonly db: Db | null = null

  async connect(): Promise<void> {
    if (this.client && this.db) {
      return // Already connected
    }

    try {
      this.client = new MongoClient(MONGODB_URI)
      await this.client.connect()
      this.db = this.client.db(DATABASE_NAME)

      console.log(`Connected to MongoDB: ${DATABASE_NAME}`)

      // Create indexes for better performance
      await this.createIndexes()
    } catch (error) {
      console.error("Failed to connect to MongoDB:", error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close()
      this.client = null
      this.db = null
      console.log("Disconnected from MongoDB")
    }
  }

  private async createIndexes(): Promise<void> {}

  async ensureConnection(): Promise<void> {
    if (!this.client || !this.db) {
      await this.connect()
    }
  }

  isConnected(): boolean {
    return this.client !== null && this.db !== null
  }
}

console.log("MONGODBURI", MONGODB_URI)
console.log("DATABASENAME", DATABASE_NAME)

// Singleton instance
export const mongodb = new MongoDB()

mongodb.connect().catch(console.error)

export async function getDatabase() {
  await mongodb.ensureConnection()
  return mongodb.db
}
