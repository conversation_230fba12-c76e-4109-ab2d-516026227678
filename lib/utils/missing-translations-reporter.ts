/**
 * Utility functions for reporting missing translations with metadata
 */

export interface MissingTranslationEntry {
  key: string
  file?: string
  line?: number
}

export interface MissingTranslationsReport {
  [namespace: string]: {
    [key: string]: MissingTranslationEntry
  }
}

/**
 * Get all missing translations from the global store (client-side)
 */
export function getMissingTranslations(): MissingTranslationsReport {
  if (typeof window === "undefined") {
    return {}
  }
  return window.__missingTranslations__ || {}
}

/**
 * Get all missing translations from the global store (server-side)
 */
export function getServerMissingTranslations(): MissingTranslationsReport {
  if (typeof global === "undefined") {
    return {}
  }
  return global.__missingTranslations__ || {}
}

/**
 * Clear all missing translations (useful for testing)
 */
export function clearMissingTranslations(): void {
  if (typeof window !== "undefined") {
    window.__missingTranslations__ = {}
  }
  if (typeof global !== "undefined") {
    global.__missingTranslations__ = {}
  }
}

/**
 * Export missing translations as JSON string
 */
export function exportMissingTranslationsAsJSON(): string {
  const clientTranslations = getMissingTranslations()
  const serverTranslations = getServerMissingTranslations()
  
  return JSON.stringify({
    client: clientTranslations,
    server: serverTranslations,
  }, null, 2)
}

/**
 * Log missing translations in a formatted way
 */
export function logMissingTranslations(): void {
  const missing = getMissingTranslations()
  
  console.group("🌐 Missing Translations Report")
  
  Object.entries(missing).forEach(([namespace, keys]) => {
    console.group(`📁 Namespace: ${namespace}`)
    
    Object.entries(keys).forEach(([key, entry]) => {
      if (entry.file && entry.line) {
        console.log(`🔍 ${key} @ ${entry.file}:${entry.line}`)
      } else {
        console.log(`🔍 ${key}`)
      }
    })
    
    console.groupEnd()
  })
  
  console.groupEnd()
}

/**
 * Development helper: Add to window for easy access in browser console
 */
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  ;(window as any).__missingTranslationsReporter = {
    get: getMissingTranslations,
    clear: clearMissingTranslations,
    export: exportMissingTranslationsAsJSON,
    log: logMissingTranslations,
  }
}
