type SimpleFieldFilter<T> = {
  field: keyof T | string
  value: any
  operator?: "EQUAL" | "NOT_EQUAL" | "IN" | "NOT_IN"
}

export function EQUAL<T>(
  field: keyof T,
  value: T[keyof T],
): SimpleFieldFilter<T> {
  return { field, value, operator: "EQUAL" }
}

export function NOT_EQUAL<T>(
  field: keyof T,
  value: T[keyof T],
): SimpleFieldFilter<T> {
  return { field, value, operator: "NOT_EQUAL" }
}

export function IN<T>(
  field: keyof T,
  value: T[keyof T][],
): SimpleFieldFilter<T> {
  return { field, value, operator: "IN" }
}

export function NOT_IN<T>(
  field: keyof T,
  value: T[keyof T][],
): SimpleFieldFilter<T> {
  return { field, value, operator: "NOT_IN" }
}

export type LogicalFilter<T> =
  | SimpleFieldFilter<T>
  | SimpleFieldFilter<T>[]
  | {
      AND?: LogicalFilter<T>[]
      OR?: LogicalFilter<T>[]
      NOT?: LogicalFilter<T>
    }

export function appendFilter<T>(
  logicalFilter: LogicalFilter<T>,
  newFilter: LogicalFilter<T>,
): LogicalFilter<T> {
  if (!logicalFilter) return newFilter
  if (!newFilter) return logicalFilter
  return {
    AND: [logicalFilter, newFilter],
  }
}

export function appendFilter2<T>(
  logicalFilter: LogicalFilter<T>,
  newFilter: SimpleFieldFilter<T>[],
): LogicalFilter<T> {
  if (!logicalFilter) return { AND: newFilter }
  if (!newFilter) return logicalFilter
  return {
    AND: [logicalFilter, { AND: newFilter }],
  }
}

export function ensureNoDuplicateFilterUsed(object: {
  filters?: any
  filterEnhanced?: any
}) {
  if (object.filters && object.filterEnhanced) {
    throw new Error("Cannot use both filters and filterEnhanced")
  }
}
