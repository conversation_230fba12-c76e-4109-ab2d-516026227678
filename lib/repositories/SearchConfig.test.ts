import { SearchConfig } from "./SearchConfig"

describe("SearchConfig", () => {
  interface TestEntity {
    id: string
    name: string
    description: string
    category: string
    isActive: boolean
    createdAt: Date
  }

  let searchConfig: SearchConfig<TestEntity>

  beforeEach(() => {
    searchConfig = new SearchConfig<TestEntity>({
      searchableFields: ["name", "description"],
      filterableFields: ["category", "isActive"],
      sortableFields: ["name", "createdAt"],
    })
  })

  describe("buildMongoQuery", () => {
    it("should build basic query with soft delete filter", () => {
      const result = searchConfig.buildMongoQuery({})

      expect(result.query).toEqual({
        deletedAt: { $exists: false },
      })
      expect(result.sort).toEqual({})
    })

    it("should include deleted items when specified", () => {
      const result = searchConfig.buildMongoQuery({
        includeDeleted: true,
      })

      expect(result.query).toEqual({})
    })

    it("should handle single filter", () => {
      const result = searchConfig.buildMongoQuery({
        filters: [{ field: "category", value: "test" }],
      })

      expect(result.query).toEqual({
        deletedAt: { $exists: false },
        category: "test",
      })
    })

    it("should handle array filter with $in operator", () => {
      const result = searchConfig.buildMongoQuery({
        filters: [{ field: "category", value: ["test1", "test2"] }],
      })

      expect(result.query).toEqual({
        deletedAt: { $exists: false },
        category: { $in: ["test1", "test2"] },
      })
    })

    it("should ignore non-filterable fields", () => {
      const result = searchConfig.buildMongoQuery({
        filters: [
          { field: "name", value: "test" }, // name is searchable but not filterable
          { field: "category", value: "valid" },
        ],
      })

      expect(result.query).toEqual({
        deletedAt: { $exists: false },
        category: "valid",
      })
    })

    it("should handle sorting", () => {
      const result = searchConfig.buildMongoQuery({
        sort: [
          { field: "name", direction: "asc" },
          { field: "createdAt", direction: "DESC" },
        ],
      })

      expect(result.sort).toEqual({
        name: 1,
        createdAt: -1,
      })
    })

    it("should ignore non-sortable fields", () => {
      const result = searchConfig.buildMongoQuery({
        sort: [
          { field: "description", direction: "asc" }, // description is searchable but not sortable
          { field: "name", direction: "DESC" },
        ],
      })

      expect(result.sort).toEqual({
        name: -1,
      })
    })

    it("should handle complex query with filters and sort", () => {
      const result = searchConfig.buildMongoQuery({
        filters: [
          { field: "category", value: "test" },
          { field: "isActive", value: true },
        ],
        sort: [{ field: "name", direction: "asc" }],
      })

      expect(result.query).toEqual({
        deletedAt: { $exists: false },
        category: "test",
        isActive: true,
      })
      expect(result.sort).toEqual({
        name: 1,
      })
    })
  })

  describe("field access", () => {
    it("should provide access to searchable fields", () => {
      expect(searchConfig.searchableFields).toEqual(["name", "description"])
    })

    it("should provide access to filterable fields", () => {
      expect(searchConfig.filterableFields).toEqual(["category", "isActive"])
    })

    it("should provide access to sortable fields", () => {
      expect(searchConfig.sortableFields).toEqual(["name", "createdAt"])
    })
  })
})
