import { readdirSync, readFileSync, writeFileSync, existsSync } from "fs"
import { join } from "path"

const sort = (o) =>
  Array.isArray(o)
    ? o.map(sort)
    : o && typeof o === "object"
      ? Object.keys(o)
          .sort()
          .reduce((a, k) => ((a[k] = sort(o[k])), a), {})
      : o

const load = (p) => JSON.parse(readFileSync(p, "utf8"))
const save = (p, o) => writeFileSync(p, JSON.stringify(sort(o), null, 2) + "\n")
const fill = (en, o) =>
  en && typeof en === "object"
    ? Object.keys(en).reduce(
        (a, k) => ((a[k] = fill(en[k], o?.[k])), a),
        o && typeof o === "object" ? { ...o } : {},
      )
    : o === undefined || o === ""
      ? en
      : o

// Global pairs (akan otomatis skip kalau foldernya tidak ada)
for (const [enDir, otherDir] of [
  ["locales/en", "locales/id"],
  ["locales/en", "locales/ja"],
  ["locales/en", "locales/ar"],
]) {
  if (!existsSync(enDir) || !existsSync(otherDir)) continue
  for (const f of readdirSync(enDir).filter((f) => f.endsWith(".json"))) {
    const enP = join(enDir, f),
      oP = join(otherDir, f)
    if (!existsSync(oP)) continue
    const en = load(enP),
      o = load(oP)
    save(enP, en)
    save(oP, fill(en, o))
  }
}

// Per-module locales
for (const dir of [
  "app/(dashboard)/contacts/locales",
  "app/(dashboard)/ai-rules/locales",
  "app/(dashboard)/message-templates/locales",
  "app/(public)/auth/locales",
  "components/locales",
]) {
  if (!existsSync(dir)) continue
  const enP = join(dir, "en.json")
  if (!existsSync(enP)) continue
  const en = load(enP)
  for (const lang of ["id", "ja", "ar"]) {
    const p = join(dir, `${lang}.json`)
    if (!existsSync(p)) continue
    const cur = load(p)
    save(p, fill(en, cur))
  }
}
console.log("Locales merged & sorted.")
