import { NextRequest, NextResponse } from "next/server"
import { queryValidation } from "@/middleware_functions/query-params-validation"
import { ResponseBuilder } from "./lib/types/responseWrapper"
import { matchPattern } from "./lib/utils/wildcardPatternMatching"

// Define secured pages (default: public)
const SECURED_PAGES = ["/dashboard", "/contacts*", "/settings"]

const INTERNAL_TOKEN_API_ROUTES = [
  "/api/v1/functions/webhook",
  "/api/v1/functions/webhook/presence",
  "/api/v1/knowledge-base/parser-callback",
  "/api/v1/ai/execution",
  "/api/v1/ai-workflow-executions/*/steps",
  "/api/v1/broadcast/send",
]

const REFRESH_ROUTE = "/api/v1/auth/refresh_token"

// Define public API routes (default: secured)
const PUBLIC_API_ROUTES = [
  "/api/health",
  "/api/v1/auth/login",
  REFRESH_ROUTE, // ✅ This should bypass auth token check
  "/api/v1/auth/temporary",
  "/api/v1/auth/validate",
  "/api/v1/auth/reset-password",
  "/api/v1/auth/verify-email",
  "/api/v1/auth/register",
  "/api/v1/auth/forgot-password",
  "/api/v1/auth/resend-verification",
  "/api/v1/public/*",
  "/api/v1/payment/*",
]

const TEST_DEV_ENABLED_API = ["/api/v1/test/*"]

const INTERNAL_SECRET_TOKEN = process.env.INTERNAL_SECRET_TOKEN || ""

export async function middleware(request: NextRequest) {
  // 1. Validate query parameters
  const invalidQueryResponse = queryValidation(request)
  if (invalidQueryResponse) return invalidQueryResponse

  const { pathname } = request.nextUrl
  const isApiRoute = pathname.startsWith("/api")

  // 2. Block test/dev APIs in production
  if (process.env.NODE_ENV === "production") {
    const isTestDevEnabledApi = TEST_DEV_ENABLED_API.some((route) =>
      matchPattern(route, pathname),
    )
    if (isTestDevEnabledApi) {
      return NextResponse.json(
        ResponseBuilder.fail(["api.error.not_found"], ["ERROR_NOT_FOUND"]),
        { status: 404 },
      )
    }
  }

  // 3. Allow internal token API routes with internal token header
  const isInternalTokenApi = INTERNAL_TOKEN_API_ROUTES.some((route) =>
    matchPattern(route, pathname),
  )
  const internalToken = request.headers.get("x-internal-system-token")

  if (isInternalTokenApi) {
    if (internalToken === INTERNAL_SECRET_TOKEN) {
      return NextResponse.next()
    } else {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["api.error.invalid_internal_token"],
          ["ERROR_UNAUTHORIZED_INTERNAL"],
        ),
        { status: 401 },
      )
    }
  }

  // 4. Match secured/public routes
  const isPublicApi = [...PUBLIC_API_ROUTES, ...TEST_DEV_ENABLED_API].some(
    (route) => matchPattern(route, pathname),
  )
  const isSecuredPage = SECURED_PAGES.some((route) =>
    matchPattern(route, pathname),
  )

  // 5. Determine if auth is required
  const requiresAuth = isApiRoute ? !isPublicApi : isSecuredPage

  if (!requiresAuth) {
    return NextResponse.next()
  }

  // ✅ 6. Allow /auth/refresh-token even if token is missing
  const isRefreshTokenEndpoint = matchPattern(REFRESH_ROUTE, pathname)
  if (isRefreshTokenEndpoint) {
    return NextResponse.next()
  }

  // 7. Enforce token cookie for all other secured routes
  const cookieToken = request.cookies.get("token")?.value
  if (!cookieToken) {
    if (isApiRoute) {
      return NextResponse.json(
        ResponseBuilder.fail(
          ["api.error.requires_token"],
          ["ERROR_UNAUTHORIZED"],
        ),
        { status: 401 },
      )
    }

    // If it's a secured page and token is missing, redirect to login
    const loginUrl = new URL("/auth/login?redirector=middleware", request.url)
    return NextResponse.redirect(loginUrl)
  }

  // 8. Token exists — forward it for internal use
  const response = NextResponse.next()
  response.headers.set("x-internal-token", cookieToken)
  return response
}
