import type { <PERSON>ada<PERSON> } from "next"
import { cookies } from "next/headers"
import { Suspense } from "react"
import "./globals.scss"

import { LoadingIndicator } from "@/components/loading-indicator"
import { Toaster as ToasterSonner } from "@/components/ui/sonner"
import { Toaster } from "@/components/ui/toaster"
import { initializeLogging } from "@/lib/logging/setup"
import { LocalizationProvider } from "@/localization/functions/localization-context"
import { ClientConfigLoader } from "./client_config"
import { kDEFAULT_LANG } from "./constant"
import { loadBusinessConfig } from "@/lib/config/business-config"

export async function generateMetadata(): Promise<Metadata> {
  const cookieStore = await cookies()
  const localeFromCookie = cookieStore.get("locale")?.value || kDEFAULT_LANG
  const business = await loadBusinessConfig(localeFromCookie)
  return {
    title: business.businessInfo.name,
    description: business.businessInfo.description,
  }
}

initializeLogging()

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const cookieStore = await cookies()
  const localeFromCookie = cookieStore.get("locale")?.value || kDEFAULT_LANG

  return (
    <html lang={localeFromCookie}>
      <body>
        <Suspense fallback={<LoadingIndicator />}>
          <ClientConfigLoader>
            <LocalizationProvider initialLocale={localeFromCookie}>
              {children}
            </LocalizationProvider>
          </ClientConfigLoader>
          <Toaster />
          <ToasterSonner richColors />
        </Suspense>
      </body>
    </html>
  )
}
