// Centralized message keys for API responses
// Re-export all message key groups

// Import all key groups
import { SUCCESS_KEYS } from "./success"
import { ERROR_KEYS } from "./error"
import { CONTACT_KEYS } from "./contact"
import { MESSAGE_TEMPLATE_KEYS } from "./message-template"
import { AI_RULE_KEYS } from "./ai-rule"
import { USER_KEYS } from "./user"
import { SEARCH_KEYS, FILTER_KEYS } from "./search"
import { SEARCH_CONFIG_KEYS } from "./search-config"

// Main MESSAGE_KEYS object that maintains backward compatibility
export const MESSAGE_KEYS = {
  // Success messages
  SUCCESS: SUCCESS_KEYS,

  // Error messages
  ERROR: ERROR_KEYS,

  // Entity-specific messages
  CONTACT: CONTACT_KEYS,
  MESSAGE_TEMPLATE: MESSAGE_TEMPLATE_KEYS,
  AI_RULE: AI_RULE_KEYS,
  USER: USER_KEYS,

  // Search and filter specific messages
  SEARCH: SEARCH_KEYS,
  FILTER: FILTER_KEYS,

  // Search configuration labels and descriptions
  SEARCH_CONFIG: SEARCH_CONFIG_KEYS,
} as const

// Re-export individual key groups for direct access
export {
  SUCCESS_KEYS,
  ERROR_KEYS,
  CONTACT_KEYS,
  MESSAGE_TEMPLATE_KEYS,
  AI_RULE_KEYS,
  USER_KEYS,
  SEARCH_KEYS,
  FILTER_KEYS,
  SEARCH_CONFIG_KEYS,
}

// Helper function to get nested message keys
export function getMessageKey(path: string): string {
  const keys = path.split(".")
  let current: any = MESSAGE_KEYS

  for (const key of keys) {
    current = current[key]
    if (!current) {
      console.warn(`Message key not found: ${path}`)
      return path // Return the path as fallback
    }
  }

  return current
}

// Type-safe message key access
export type MessageKeyPath =
  | `SUCCESS.${keyof typeof SUCCESS_KEYS}`
  | `ERROR.${keyof typeof ERROR_KEYS}`
  | `CONTACT.${keyof typeof CONTACT_KEYS}`
  | `MESSAGE_TEMPLATE.${keyof typeof MESSAGE_TEMPLATE_KEYS}`
  | `AI_RULE.${keyof typeof AI_RULE_KEYS}`
  | `USER.${keyof typeof USER_KEYS}`
  | `SEARCH.${keyof typeof SEARCH_KEYS}`
  | `FILTER.${keyof typeof FILTER_KEYS}`
  | `SEARCH_CONFIG.${keyof typeof SEARCH_CONFIG_KEYS}`

export function getTypedMessageKey(path: MessageKeyPath): string {
  return getMessageKey(path)
}

// Backward compatibility - export the old structure
export default MESSAGE_KEYS
