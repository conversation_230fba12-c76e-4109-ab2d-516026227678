import { NextResponse } from "next/server"
import { BaseSearchConfig } from "./v1/search-configs/entities/baseSearchConfig"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "./error_codes"

type ParsedSearchParams = {
  search?: string
  includeDeleted: boolean
  page?: number
  limit?: number
  sort: any[]
  filters: any[]
}

type ParseSearchParamsResult =
  | { params: ParsedSearchParams; response?: undefined }
  | { response: ReturnType<typeof NextResponse.json>; params?: undefined }

export function parseSearchParams(
  searchParams: URLSearchParams,
  searchConfig: BaseSearchConfig,
): ParseSearchParamsResult {
  const search = searchParams.get("search") || undefined
  const includeDeleted = searchParams.get("includeDeleted") === "true"
  const page = searchParams.get("page")
    ? parseInt(searchParams.get("page")!)
    : undefined
  const limit = searchParams.get("limit")
    ? parseInt(searchParams.get("limit")!)
    : undefined

  const { result: searchConfigResult, errors: searchConfigErrors } =
    searchConfig.parseParams(searchParams)

  if (searchConfigErrors) {
    return {
      response: NextResponse.json(
        new ResponseWrapper("failed", undefined, searchConfigErrors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
        { status: 400 },
      ),
    }
  }

  const sort = searchConfigResult?.sort || []
  const filters = searchConfigResult?.filters || []

  return {
    params: {
      search,
      includeDeleted,
      page,
      limit,
      sort,
      filters,
    },
  }
}
