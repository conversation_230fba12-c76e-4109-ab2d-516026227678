import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { providers } from "@/lib/providers"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { loggedApiRoute } from "@/lib/logging/middleware"
import { createContextLogger } from "@/lib/logging"
import z from "zod"
import { ERROR_CODES } from "@/app/api/error_codes"

const schema = z.object({
  isActive: z.boolean(),
})

export const POST = loggedApiRoute(
  async (req: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { context, response } = await buildSessionContext(req)
      if (response) {
        return response
      }

      const body = await req.json()
      const { id } = await params

      // Create context logger for this request
      const logger = createContextLogger({
        component: "device-management",
        userId: context.user.id,
        organizationId: context.organization?.id,
      })

      logger.info("Device toggle state request", {
        deviceId: id,
        requestedState: body.isActive,
        feature: "toggle-state",
      })

      // Validate request body
      const validationResult = schema.safeParse(body)
      if (!validationResult.success) {
        return NextResponse.json(
          new ResponseWrapper(
            "failed",
            undefined,
            ["Invalid request body"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
          { status: 400 },
        )
      }

      const { isActive } = validationResult.data

      // Get the device first
      const device = await devicesBusinessLogic.getById(id, context)
      if (!device) {
        return NextResponse.json(
          new ResponseWrapper(
            "failed",
            undefined,
            ["Device not found"],
            [ERROR_CODES.NOT_FOUND],
          ),
          { status: 404 },
        )
      }

      // Get the provider
      const provider = providers[process.env.CONVERSATION_PROVIDER!]
      if (!provider) {
        return NextResponse.json(
          new ResponseWrapper(
            "failed",
            undefined,
            ["Provider not configured"],
            [ERROR_CODES.PROVIDER_NOT_FOUND],
          ),
          { status: 500 },
        )
      }

      // Call provider to start/stop session
      const providerStart = Date.now()
      try {
        logger.info(`${isActive ? "Starting" : "Stopping"} provider session`, {
          deviceId: id,
          sessionId: device.sessionId,
          provider: provider.name,
        })

        if (isActive) {
          // Start the session
          if (provider.restartSession) {
            await provider.restartSession(device.sessionId)
          }
        } else {
          // Stop the session
          if (provider.stopSession) {
            await provider.stopSession(device.sessionId)
          }
        }

        logger.info("Provider session operation completed", {
          deviceId: id,
          operation: isActive ? "start" : "stop",
          duration: Date.now() - providerStart,
        })
      } catch (providerError: any) {
        logger.error("Provider session operation failed", providerError, {
          deviceId: id,
          operation: isActive ? "start" : "stop",
          provider: provider.name,
          duration: Date.now() - providerStart,
        })

        return NextResponse.json(
          new ResponseWrapper(
            "failed",
            undefined,
            [
              `Failed to ${isActive ? "start" : "stop"} session: ${providerError.message}`,
            ],
            [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
          ),
          { status: 500 },
        )
      }

      // Update the device state in database
      const updatedDevice = await devicesBusinessLogic.update(
        id,
        { isActive },
        context,
      )
      if (!updatedDevice) {
        return NextResponse.json(
          new ResponseWrapper(
            "failed",
            undefined,
            ["Failed to update device state"],
            [ERROR_CODES.UPDATE_FAILED],
          ),
          { status: 500 },
        )
      }

      return NextResponse.json(new ResponseWrapper("success", updatedDevice), {
        status: 200,
      })
    } catch (error: any) {
      console.error("Toggle device state error:", error)
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Failed to toggle device state. Please try again."],
          [ERROR_CODES.INTERNAL_SERVER_ERROR],
        ),
        { status: 500 },
      )
    }
  },
)
