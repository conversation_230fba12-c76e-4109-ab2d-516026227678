import { NextRequest, NextResponse } from "next/server"
import { implHandleSyncAllSessions } from "../impl"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const result = await implHandleSyncAllSessions(context)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in POST /api/v1/devices/sync-all:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
