import { providers } from "@/lib/providers"
import { SessionContext } from "@/lib/repositories/auth/types"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "../../error_codes"

export async function implHandleGetDevices(
  searchParams: URLSearchParams,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    const page = parseInt(searchParams.get("page") || "1")
    const per_page = parseInt(searchParams.get("per_page") || "10")
    const search = searchParams.get("search") || undefined
    const status = searchParams.get("status") || undefined
    const isActive = searchParams.get("isActive")
      ? searchParams.get("isActive") === "true"
      : undefined

    const result = await devicesBusinessLogic.getAll(
      {
        offset: page,
        limit: per_page,
        search,
        status: status as any,
        isActive,
        sort: [{ field: "createdAt", direction: "DESC" }],
      },
      context,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        items: result.items,
        total: result.total,
        page,
        per_page,
      }),
    }
  } catch (error: any) {
    console.error("Get devices error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch devices. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

export async function implHandleCreateDevice(
  body: any,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    const device = await devicesBusinessLogic.create(body, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", device),
    }
  } catch (error: any) {
    console.error("Create device error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create device. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

export async function implHandleUpdateDevice(
  deviceId: string,
  body: any,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    const device = await devicesBusinessLogic.update(deviceId, body, context)

    if (!device) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Device not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", device),
    }
  } catch (error: any) {
    console.error("Update device error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update device. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

export async function implHandleDeleteDevice(
  deviceId: string,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    const device = await devicesBusinessLogic.getById(deviceId, context)
    if (!device) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Device not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }
    const provider = providers[process.env.CONVERSATION_PROVIDER!]

    const result = await provider.logoutSession(device.sessionId)
    console.log("Logout Session Device result:", result)

    const success = await devicesBusinessLogic.delete(deviceId, context)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Device not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { deleted: true }),
    }
  } catch (error: any) {
    console.error("Delete device error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete device. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

export async function implHandleSyncDevices(context: SessionContext): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    const syncResult = await devicesBusinessLogic.syncWithProvider(context)

    return {
      status: 200,
      body: new ResponseWrapper("success", syncResult),
    }
  } catch (error: any) {
    console.error("Sync devices error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to sync devices. Please try again."],
        [ERROR_CODES.SYNC_FAILED],
      ),
    }
  }
}

export async function implHandleSyncAllSessions(
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const { devicesBusinessLogic } = getBusinessLogics()
  try {
    // Get all active devices for this organization
    const devicesResult = await devicesBusinessLogic.getAll(
      {
        offset: 1,
        limit: 1000, // Get all devices
        isActive: true,
      },
      context,
    )

    const provider = providers[process.env.CONVERSATION_PROVIDER!]
    const syncResults = {
      synced: 0,
      created: 0,
      updated: 0,
      errors: [] as string[],
    }

    // Sync each session individually
    for (const device of devicesResult.items) {
      try {
        console.log(`Syncing session: ${device.sessionId}`)

        // Call provider's sync method for individual session
        const sessionSyncResult = await provider.syncSession(device.sessionId)

        if (sessionSyncResult.success) {
          syncResults.synced++

          // Update device status based on sync result
          if (sessionSyncResult.status) {
            await devicesBusinessLogic.update(
              device.id,
              {
                status: sessionSyncResult.status?.toString() as any,
                lastSeenAt: new Date(),
                me: sessionSyncResult.me || device.me,
                providerData:
                  sessionSyncResult.providerData || device.providerData,
              },
              context,
            )
            syncResults.updated++
          }
        } else {
          syncResults.errors.push(
            `Failed to sync session ${device.sessionId}: ${sessionSyncResult.error || "Unknown error"}`,
          )
        }
      } catch (sessionError: any) {
        console.error(
          `Error syncing session ${device.sessionId}:`,
          sessionError,
        )
        syncResults.errors.push(
          `Session ${device.sessionId}: ${sessionError.message || "Sync failed"}`,
        )
      }
    }

    // Also run the general sync to catch any new sessions
    try {
      const generalSyncResult =
        await devicesBusinessLogic.syncWithProvider(context)
      syncResults.created += generalSyncResult.created
      syncResults.synced += generalSyncResult.synced
      syncResults.updated += generalSyncResult.updated
      syncResults.errors.push(...generalSyncResult.errors)
    } catch (generalSyncError: any) {
      console.error("General sync error:", generalSyncError)
      syncResults.errors.push(
        `General sync failed: ${generalSyncError.message}`,
      )
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", syncResults, [
        `Sync completed: ${syncResults.synced} sessions synced, ${syncResults.created} created, ${syncResults.updated} updated`,
      ]),
    }
  } catch (error: any) {
    console.error("Sync all sessions error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to sync all sessions. Please try again."],
        [ERROR_CODES.SYNC_FAILED],
      ),
    }
  }
}
