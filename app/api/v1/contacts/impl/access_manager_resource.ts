import {
  AccessRule,
  GroupRoleResourceAccessManager,
} from "@/lib/repositories/AccessManager"

// Use wildcards only; no ":id"
export const AccessManagerResource = [
  "* /contacts*",
  "GET /contacts",
  "GET /contacts/stats",
  // replaced all ":id" with "*"
  "GET /contacts/*",
  "POST /contacts",
  "PUT /contacts/*",
  "DELETE /contacts/*",
  "DELETE /bulk",
  "POST /bulk",
  "PUT /bulk",
] as const

export type ContactAccessResource = (typeof AccessManagerResource)[number]

export async function setInitialContactAccessOnRegister(
  groupId: string,
  accessManager: GroupRoleResourceAccessManager<ContactAccessResource>,
): Promise<void> {
  await accessManager.appendRules([
    // Admin – full access
    {
      resource: "* /contacts*",
      group: groupId,
      role: "admin",
      actions: ["read", "create", "update", "delete"],
    },

    {
      resource: "GET /contacts",
      group: groupId,
      role: "member",
      actions: ["read"],
    },
    // changed from "GET /contacts/:id" to wildcard
    {
      resource: "GET /contacts/*",
      group: groupId,
      role: "member",
      actions: ["read"],
    },
    // changed from "PUT /contacts/:id" to wildcard
    {
      resource: "PUT /contacts/*",
      group: groupId,
      role: "member",
      actions: ["update"],
    },
  ])
}
