import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ContactAccessResource } from "./access_manager_resource"
import { ContactResponses } from "@/lib/utils/api-response-helper"

// Delete Contact Implementation
export async function implHandleDeleteContact(
  id: string,
  businessLogic: ContactBusinessLogicInterface,
  hardDelete: boolean = false,
  context: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<ContactAccessResource>,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  // const isAuthorized = await accessManager?.isAllowed({
  //   resource: "DELETE /contacts/*",
  //   action: "delete",
  //   userId: context?.user.id ?? "",
  // })

  // if (!isAuthorized) {
  //   return ContactResponses.unauthorizedRole()
  // }

  try {
    await businessLogic.delete(id, context, hardDelete)

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Contact deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete contacts error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete contacts. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}
