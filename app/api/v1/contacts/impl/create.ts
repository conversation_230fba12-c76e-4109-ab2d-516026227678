import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ContactCreateSchema } from "@/lib/validations/contact"
import { ERROR_CODES } from "@/app/api/error_codes"
import {
  ContactResponses,
  createValidationErrorResponse,
} from "@/lib/utils/api-response-helper"
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ContactAccessResource } from "./access_manager_resource"

// Create Contact Implementation
export async function implHandleCreateContact(
  data: any,
  businessLogic: ContactBusinessLogicInterface,
  context: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<ContactAccessResource>,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  // const isAuthorized = await accessManager?.isAllowed({
  //   resource: "POST /contacts",
  //   action: "create",
  //   userId: context?.user.id ?? "",
  // })

  // if (!isAuthorized) {
  //   return ContactResponses.unauthorizedRole()
  // }

  try {
    // Validate input data
    const validationResult = ContactCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return createValidationErrorResponse(errors)
    }

    // Transform notes to include createdAt if not present
    const createData = {
      ...validationResult.data,
      notes: validationResult.data.notes?.map((note) => ({
        text: note.text,
        createdAt: new Date().toISOString(),
      })),
    }

    const contacts = await businessLogic.create(createData, context)

    return ContactResponses.created(contacts)
  } catch (error: any) {
    console.error("Create contacts error:", error)

    if (error.code === "DUPLICATE_PHONE") {
      return ContactResponses.duplicatePhone()
    }

    if (error.code === "DUPLICATE_NAME") {
      return ContactResponses.duplicateName()
    }

    if (error.code === "INVALID_EMAIL") {
      return createValidationErrorResponse([error.message])
    }

    return ContactResponses.createFailed()
  }
}
