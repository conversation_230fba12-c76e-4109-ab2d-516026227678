import {
  CustomerProfileBusinessLogicInterface,
  CustomerProfile,
} from "@/lib/repositories/customerProfiles/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"

type GetAllResultPaginated<T> = {
  items: T[]
  page: number
  total: number
}

// Get Customer Profile by ID Implementation
export async function implHandleGetCustomerProfile(
  id: string,
  businessLogic: CustomerProfileBusinessLogicInterface,
  context: SessionContext,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const customerProfile = await businessLogic.getById(
      id,
      context,
      includeDeleted,
    )

    if (!customerProfile) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Customer profile not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", customerProfile),
    }
  } catch (error: any) {
    console.error("Get customer profile error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch customer profile. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

// Get Customer Profile by Customer ID Implementation
export async function implHandleGetCustomerProfileByCustomerId(
  customerId: string,
  businessLogic: CustomerProfileBusinessLogicInterface,
  context: SessionContext,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const customerProfile = await businessLogic.getByCustomerId(
      customerId,
      context,
      includeDeleted,
    )

    if (!customerProfile) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Customer profile not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", customerProfile),
    }
  } catch (error: any) {
    console.error("Get customer profile by customer ID error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch customer profile. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

// Get All Customer Profiles Implementation
export async function implHandleGetAllCustomerProfiles(
  businessLogic: CustomerProfileBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof CustomerProfile | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof CustomerProfile | string
      value: CustomerProfile[keyof CustomerProfile] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<CustomerProfile>>
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (typeof params.search !== "string") {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<CustomerProfile>>(
            "failed",
            undefined,
            ["Search parameter must be a string"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    // Build query parameters
    const queryParams: any = {
      search: params?.search,
      includeDeleted: params?.includeDeleted || false,
    }

    // Add pagination if provided
    if (params?.page !== undefined && params?.limit !== undefined) {
      queryParams.offset = (params.page - 1) * params.limit
      queryParams.limit = params.limit
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params?.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(queryParams, context)

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<CustomerProfile>>(
        "success",
        {
          items: result.items,
          page: params?.page || 1,
          total: result.total,
        },
      ),
    }
  } catch (error: any) {
    console.error("Get customer profiles error:", error)

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<CustomerProfile>>(
        "failed",
        undefined,
        ["Failed to fetch customer profiles. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}
