import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"
import { KnowledgeBaseBusinessLogicInterface } from "@/lib/repositories/knowledgeBase/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  KnowledgeBaseCreateSchema,
  KnowledgeBaseExtractKeywordsSchema,
  KnowledgeBaseParseContentSchema,
  KnowledgeBaseQuerySchema,
  KnowledgeBaseSearchSchema,
  KnowledgeBaseUpdateSchema,
} from "@/lib/validations/knowledgeBase"

interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

import {
  KnowledgeBaseEntry,
  KnowledgeBaseStats,
} from "@/lib/repositories/knowledgeBase/interface"

// Create Knowledge Base Entry Implementation
export async function implHandleCreateKnowledgeBase(
  data: any,
  businessLogic: KnowledgeBaseBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validationResult = KnowledgeBaseCreateSchema.safeParse(data)
    if (!validationResult.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Add context fields to the validated data
    const entryData = {
      ...validationResult.data,
      createdBy: context.user.id,
      organizationId: context.organization?.id,
    }

    const entry = await businessLogic.create(entryData, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", entry, [
        "Knowledge base entry created successfully",
      ]),
    }
  } catch (error: any) {
    console.error("Error creating knowledge base entry:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to create knowledge base entry"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Update Knowledge Base Entry Implementation
export async function implHandleUpdateKnowledgeBase(
  id: string,
  data: any,
  businessLogic: KnowledgeBaseBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate ID
    if (!id || typeof id !== "string" || id.trim() === "") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Knowledge base entry ID is required"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const validationResult = KnowledgeBaseUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const entry = await businessLogic.update(id, validationResult.data, context)

    if (!entry) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Knowledge base entry not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", entry, [
        "Knowledge base entry updated successfully",
      ]),
    }
  } catch (error: any) {
    console.error("Error updating knowledge base entry:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to update knowledge base entry"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Delete Knowledge Base Entry Implementation
export async function implHandleDeleteKnowledgeBase(
  id: string,
  businessLogic: KnowledgeBaseBusinessLogicInterface,
  context: SessionContext,
  hardDelete = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Knowledge base entry not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { deleted: true }, [
        hardDelete
          ? "Knowledge base entry permanently deleted"
          : "Knowledge base entry deleted",
      ]),
    }
  } catch (error: any) {
    console.error("Error deleting knowledge base entry:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to delete knowledge base entry"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Get Knowledge Base Entry by ID Implementation
export async function implHandleGetKnowledgeBaseById(
  id: string,
  businessLogic: KnowledgeBaseBusinessLogicInterface,
  context: SessionContext,
  includeDeleted = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const entry = await businessLogic.getById(id, context, includeDeleted)

    if (!entry) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Knowledge base entry not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", entry, [
        "Knowledge base entry retrieved successfully",
      ]),
    }
  } catch (error: any) {
    console.error("Error retrieving knowledge base entry:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to retrieve knowledge base entry"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Get All Knowledge Base Entries Implementation
export async function implHandleGetAllKnowledgeBase(
  businessLogic: KnowledgeBaseBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    category?: string
    tags?: string[]
    isActive?: boolean
    sortBy?: string
    sortOrder?: string
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<KnowledgeBaseEntry> | undefined>
}> {
  try {
    const validationResult = KnowledgeBaseQuerySchema.safeParse(params)
    if (!validationResult.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const queryParams = {
      search: validationResult.data.search,
      page: validationResult.data.page,
      limit: validationResult.data.limit,
      includeDeleted: validationResult.data.includeDeleted,
      filters: [
        ...(validationResult.data.category
          ? [{ field: "category", value: validationResult.data.category }]
          : []),
        ...(validationResult.data.tags
          ? [{ field: "tags", value: validationResult.data.tags }]
          : []),
        ...(validationResult.data.isActive !== undefined
          ? [{ field: "isActive", value: validationResult.data.isActive }]
          : []),
      ],
      sort: [
        {
          field: validationResult.data.sortBy,
          direction: validationResult.data.sortOrder as "ASC" | "DESC",
        },
      ],
    }

    const result = await businessLogic.getAll(queryParams, context)

    const response: GetAllResultPaginated<KnowledgeBaseEntry> = {
      items: result.items,
      total: result.total,
      page: validationResult.data.page,
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", response),
    }
  } catch (error: any) {
    console.error("Error retrieving knowledge base entries:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to retrieve knowledge base entries"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Extract Keywords Implementation
export async function implHandleExtractKeywords(
  data: any,
  businessLogic: KnowledgeBaseBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validationResult = KnowledgeBaseExtractKeywordsSchema.safeParse(data)
    if (!validationResult.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const keywords = await businessLogic.extractKeywords(
      validationResult.data.content,
      context,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", { keywords }, [
        "Keywords extracted successfully",
      ]),
    }
  } catch (error: any) {
    console.error("Error extracting keywords:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to extract keywords"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Search Similar Knowledge Base Entries Implementation
export async function implHandleSearchKnowledgeBase(
  data: any,
  businessLogic: KnowledgeBaseBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validationResult = KnowledgeBaseSearchSchema.safeParse(data)
    if (!validationResult.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    const entries = await businessLogic.searchSimilar(
      validationResult.data.query,
      context,
      validationResult.data.limit,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", entries, [
        "Similar knowledge base entries found",
      ]),
    }
  } catch (error: any) {
    console.error("Error searching knowledge base:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to search knowledge base"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}

// Get Knowledge Base Stats Implementation
export async function implHandleGetKnowledgeBaseStats(
  businessLogic: KnowledgeBaseBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<KnowledgeBaseStats | undefined>
}> {
  try {
    const stats = await businessLogic.getStats(context)

    return {
      status: 200,
      body: new ResponseWrapper("success", stats, [
        "Knowledge base stats retrieved successfully",
      ]),
    }
  } catch (error: any) {
    console.error("Error retrieving knowledge base stats:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        [error.message || "Failed to retrieve knowledge base stats"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
    }
  }
}
