import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetKnowledgeBaseById,
  implHandleUpdateKnowledgeBase,
  implHandleDeleteKnowledgeBase,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// GET /api/v1/knowledge-base/[id] - Get knowledge base entry by ID
export async function GET(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { knowledgeBaseBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const { searchParams } = new URL(req.url)
    const includeDeleted = searchParams.get("includeDeleted") === "true"

    const result = await implHandleGetKnowledgeBaseById(
      id,
      knowledgeBaseBusinessLogic,
      context,
      includeDeleted,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in GET /api/v1/knowledge-base/[id]:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

// PUT /api/v1/knowledge-base/[id] - Update knowledge base entry
export async function PUT(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { knowledgeBaseBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const body = await req.json()

    const result = await implHandleUpdateKnowledgeBase(
      id,
      body,
      knowledgeBaseBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in PUT /api/v1/knowledge-base/[id]:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

// DELETE /api/v1/knowledge-base/[id] - Delete knowledge base entry
export async function DELETE(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { knowledgeBaseBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const { searchParams } = new URL(req.url)
    const hardDelete = searchParams.get("hardDelete") === "true"

    const result = await implHandleDeleteKnowledgeBase(
      id,
      knowledgeBaseBusinessLogic,
      context,
      hardDelete,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in DELETE /api/v1/knowledge-base/[id]:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
