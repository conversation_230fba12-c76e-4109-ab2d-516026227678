import { driver } from "@/lib/repositories/LiveRedisDriver"

export async function getGlobalAISetting(userId: string): Promise<boolean> {
  try {
    const redisKey = `global_ai_enabled_new_conversation_setting-${userId}`
    const storedValue = await driver.get(redisKey)
    // Default is false (disabled) if not set
    return storedValue ? JSON.parse(storedValue as string) : false
  } catch (error) {
    console.error("Error getting global AI setting:", error)
    // Default to false on error
    return false
  }
}

export async function setGlobalAISetting(
  userId: string,
  enabled: boolean,
): Promise<void> {
  try {
    const redisKey = `global_ai_enabled_new_conversation_setting-${userId}`
    await driver.set(redisKey, JSON.stringify(enabled))
  } catch (error) {
    console.error("Error setting global AI setting:", error)
    throw error
  }
}
