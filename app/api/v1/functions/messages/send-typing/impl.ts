import { ERROR_CODES } from "@/app/api/error_codes"
import { providers } from "@/lib/providers"
import { SessionContext } from "@/lib/repositories/auth/types"
import { SendTypingSchema } from "@/lib/schemas/messages"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { getSessionToConversationStore } from "../../webhook/SessionToConversationRoomStore"

export async function implHandleSendTyping(
  body: any,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate request body
    const validationResult = SendTypingSchema.safeParse(body)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const { status: typingStatus, conversationId } = validationResult.data

    const entry =
      await getSessionToConversationStore().getByConversationId(conversationId)
    if (!entry) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Session for Conversation room not found."],
          [ERROR_CODES.SESSION_REQUIRED],
        ),
      }
    }
    const session = entry.session

    const provider = providers[process.env.CONVERSATION_PROVIDER!]
    if (!provider) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [`Provider waha is not available.`],
          [ERROR_CODES.PROVIDER_NOT_FOUND],
        ),
      }
    }

    await provider.sendTyping(typingStatus, conversationId, session)

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        provider: provider.name,
      }),
    }
  } catch (error: any) {
    console.error("Send typing error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to send typing status. Please try again."],
        [ERROR_CODES.SEND_TYPING_FAILED],
      ),
    }
  }
}
