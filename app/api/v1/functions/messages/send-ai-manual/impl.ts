import { ERROR_CODES } from "@/app/api/error_codes"
import { providers } from "@/lib/providers"
import {
  RealtimeConversationList,
  RealtimeConversationRoom,
} from "@/lib/realtime/model"
import { SessionContext } from "@/lib/repositories/auth/types"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  ConversationMessageStatus,
  ConversationMessageUpdateInput,
} from "@/lib/repositories/conversationMessages"
import { SendMessagesAIManualSchema } from "@/lib/schemas/messages"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { enrichParticipantInfo } from "../../../conversations/impl"
import { getSessionToConversationStore } from "../../webhook/SessionToConversationRoomStore"

export async function implHandleSendMessageAIManual(
  body: {
    conversationId: string
    messageId: string
  },
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const { conversationBusinessLogic, conversationMessagesBusinessLogic } =
    getBusinessLogics()
  try {
    const validationResult = SendMessagesAIManualSchema.safeParse(body)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const { conversationId, messageId } = validationResult.data

    const conversation = await conversationBusinessLogic.getById(
      conversationId,
      context,
    )

    if (!conversation) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          ["CONVERSATION_NOT_FOUND"],
        ),
      }
    }

    let messageStatus: ConversationMessageStatus
    const storedMessage = await conversationMessagesBusinessLogic.getById(
      messageId,
      context,
    )

    if (!storedMessage) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Message not found"],
          ["MESSAGE_NOT_FOUND"],
        ),
      }
    }

    const provider = providers[process.env.CONVERSATION_PROVIDER!]

    const entry =
      await getSessionToConversationStore().getByConversationId(conversationId)
    if (!entry) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Session for Conversation room not found."],
          [ERROR_CODES.SESSION_REQUIRED],
        ),
      }
    }
    const session = entry.session
    const waha_conversation_id = entry.waha_conversation_id

    if (!session) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Session is required for sending messages."],
          [ERROR_CODES.SESSION_REQUIRED],
        ),
      }
    }
    try {
      const response = await provider.sendMessage(
        waha_conversation_id,
        storedMessage.content,
        session,
      )
      messageStatus = "SENT"
    } catch {
      messageStatus = "FAILED"
    }

    const messageInput: ConversationMessageUpdateInput = {
      status: messageStatus,
    }

    const message = await conversationMessagesBusinessLogic.update(
      messageId,
      messageInput,
      context,
    )

    if (!message) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Message not found"],
          ["MESSAGE_NOT_FOUND"],
        ),
      }
    }

    await RealtimeConversationRoom.UPDATE_MESSAGE(
      conversationId,
      message,
    ).send()
    const updatedConversation = await conversationBusinessLogic.update(
      conversationId,
      {
        isAiAnswering: false,
        lastMessage: {
          body: storedMessage.content,
          fromMe: true,
          _data: undefined,
          ack: 1,
        },
        lastMessageAt: new Date(),
      },
      context,
    )

    if (!conversation) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }
    const enriched = await enrichParticipantInfo(context, [conversation])
    await RealtimeConversationRoom.UPDATE_CONVERSATION(enriched[0]).send()
    await RealtimeConversationList.UPDATE_CONVERSATION(enriched[0]).send()

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message,
      }),
    }
  } catch (error: any) {
    console.error("Send message error:", error)

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to send message. Please try again."],
        [ERROR_CODES.SEND_FAILED],
      ),
    }
  }
}
