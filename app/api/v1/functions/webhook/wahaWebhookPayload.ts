export interface WahaWebhookPayload {
  id: string
  timestamp: number
  event: "message"
  session: string
  metadata: Record<string, any>
  me: {
    id: string
    pushName: string
  }
  payload: MessagePayload
  engine: string
  environment: Environment
}

export interface MessagePayload {
  id: string
  timestamp: number
  from: string
  fromMe: boolean
  source: string
  body: string
  hasMedia: boolean
  media: any | null
  ack: number
  ackName: string
  replyTo: any | null
  _data: MessageData
}

export interface MessageData {
  key: {
    remoteJid: string
    fromMe: boolean
    id: string
  }
  messageTimestamp: number
  pushName: string
  broadcast: boolean
  message: {
    conversation: string
    messageContextInfo?: {
      deviceListMetadata?: {
        recipientKeyHash: string
        recipientTimestamp: string
      }
      deviceListMetadataVersion?: number
      messageSecret?: string
    }
  }
  verifiedBizName?: string
  status: number
}

export interface Environment {
  version: string
  engine: string
  tier: string
  browser: string | null
}
