import { loggedApiRoute } from "@/lib/logging/middleware"
import { NextRequest, NextResponse } from "next/server"
import { processWebhook } from "./impl"

export const POST = loggedApiRoute(async (req: NextRequest) => {
  const response = await processWebhook(req)
  if (response.status === "success") {
    return NextResponse.json(response, { status: 200 })
  } else {
    return NextResponse.json(response, { status: 400 })
  }
})
