// app/api/websocket/trigger/route.ts
import { NextRequest, NextResponse } from "next/server"

// This would be your WebSocket server instance
// You'll need to implement a WebSocket server that maintains connections
// and can broadcast messages to specific channels

// Example implementation using a simple in-memory store
// In production, you'd want to use Redis or another persistent store
class WebSocketManager {
  private static instance: WebSocketManager
  private connections: Map<string, Set<WebSocket>> = new Map()

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager()
    }
    return WebSocketManager.instance
  }

  addConnection(channel: string, ws: WebSocket): void {
    if (!this.connections.has(channel)) {
      this.connections.set(channel, new Set())
    }
    this.connections.get(channel)!.add(ws)
  }

  removeConnection(channel: string, ws: WebSocket): void {
    const channelConnections = this.connections.get(channel)
    if (channelConnections) {
      channelConnections.delete(ws)
      if (channelConnections.size === 0) {
        this.connections.delete(channel)
      }
    }
  }

  broadcast(channel: string, event: string, data: any): void {
    const channelConnections = this.connections.get(channel)
    if (channelConnections) {
      const message = JSON.stringify({
        type: "event",
        channel,
        event,
        data,
      })

      channelConnections.forEach((ws) => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(message)
        }
      })
    }
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { type, channel, event, data } = body

    if (type !== "trigger") {
      return NextResponse.json(
        { success: false, error: "Invalid request type" },
        { status: 400 },
      )
    }

    if (!channel || !event) {
      return NextResponse.json(
        { success: false, error: "Channel and event are required" },
        { status: 400 },
      )
    }

    // Broadcast the message to all connected clients on this channel
    const wsManager = WebSocketManager.getInstance()
    wsManager.broadcast(channel, event, data)

    return NextResponse.json({
      success: true,
      message: `Event "${event}" triggered on channel "${channel}"`,
    })
  } catch (error: any) {
    console.error("WebSocket trigger error:", error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 },
    )
  }
}
