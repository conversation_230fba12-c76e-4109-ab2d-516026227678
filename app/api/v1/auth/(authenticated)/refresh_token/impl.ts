import { AuthBusinessLogicInterface } from "@/lib/repositories/auth"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { RefreshTokenSchema } from "@/lib/schemas/auth"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleRefreshToken(
  cookie: {
    refresh_token: string
    token?: string
  },
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<
    | {
        token: string
        refresh_token: string
      }
    | undefined
  >
}> {
  try {
    const result = await authBusinessLogic.refreshToken({
      token: cookie.token,
      refresh_token: cookie.refresh_token,
    })

    return {
      status: 200,
      body: new ResponseWrapper("success", result),
    }
  } catch (error: any) {
    console.error("Token refresh error:", error)

    if (error.code === "INVALID_TOKEN") {
      return {
        status: 401,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Invalid or expired refresh token"],
          [ERROR_CODES.INVALID_TOKEN],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Token refresh failed. Please login again."],
        [ERROR_CODES.REFRESH_FAILED],
      ),
    }
  }
}
