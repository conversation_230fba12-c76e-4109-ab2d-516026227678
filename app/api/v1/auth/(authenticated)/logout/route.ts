import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleLogout } from "./impl"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function POST(req: NextRequest) {
  const { authBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const token = req.headers.get("x-internal-token")!
    const result = await implHandleLogout(token, authBusinessLogic)
    const resp = NextResponse.json(result.body, { status: result.status })
    resp.cookies.set("token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 0,
    })
    resp.cookies.set("refresh_token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 0,
    })
    return resp
  } catch (error) {
    console.error("Logout route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 },
    )
  }
}
