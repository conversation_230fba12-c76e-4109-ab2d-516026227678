import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { LogoutSchema } from "@/lib/schemas/auth"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function implHandleLogout(
  token: string,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    await authBusinessLogic.logout(token)

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Logged out successfully",
      }),
    }
  } catch (error) {
    console.error("Logout error:", error)
    return {
      status: 500,
      body: new ResponseWrapper("failed", null, ["Internal server error"]),
    }
  }
}
