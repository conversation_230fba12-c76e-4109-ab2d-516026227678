import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { DeleteAccountSchema } from "@/lib/schemas/auth"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function implHandleDeleteAccount(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
  userId: string,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const validation = DeleteAccountSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
        ),
      }
    }

    const { password } = validation.data

    // Delete account
    const result = await authBusinessLogic.deleteAccount(userId, password)

    if (!result.success) {
      return {
        status: 400,
        body: new ResponseWrapper("failed", null, [result.message]),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: result.message }),
    }
  } catch (error) {
    console.error("Delete account error:", error)
    return {
      status: 500,
      body: new ResponseWrapper("failed", null, ["Internal server error"]),
    }
  }
}
