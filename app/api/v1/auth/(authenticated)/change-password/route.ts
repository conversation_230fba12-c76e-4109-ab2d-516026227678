import { NextRequest, NextResponse } from "next/server"
import { implHandleChangePassword } from "./impl"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function POST(req: NextRequest) {
  const { authBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleChangePassword(
      body,
      authBusinessLogic,
      context.user.id,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Change password route error:", error)
    return NextResponse.json(
      new ResponseWrapper("failed", null, ["Internal server error"]),
      { status: 500 },
    )
  }
}
