import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleResetPassword } from "./impl"

export async function POST(req: NextRequest) {
  const { authBusinessLogic } = getBusinessLogics()
  try {
    const body = await req.json()
    const result = await implHandleResetPassword(body, authBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Reset password route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 },
    )
  }
}
