import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface"
import { VerifyEmailSchema } from "@/lib/schemas/auth"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function implHandleVerifyEmail(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate request body
    const validation = VerifyEmailSchema.safeParse(body)
    if (!validation.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
        ),
      }
    }

    const { token } = validation.data

    // Verify email
    const result = await authBusinessLogic.verifyEmail(token)

    if (!result.success) {
      return {
        status: 400,
        body: new ResponseWrapper("failed", null, [result.message]),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: result.message }),
    }
  } catch (error) {
    console.error("Verify email error:", error)
    return {
      status: 500,
      body: new ResponseWrapper("failed", null, ["Internal server error"]),
    }
  }
}
