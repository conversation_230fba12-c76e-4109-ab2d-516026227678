import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleVerifyEmail } from "./impl"

export async function POST(req: NextRequest) {
  const { authBusinessLogic } = getBusinessLogics()
  try {
    const body = await req.json()
    const result = await implHandleVerifyEmail(body, authBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Verify email route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: [],
      },
      { status: 500 },
    )
  }
}
