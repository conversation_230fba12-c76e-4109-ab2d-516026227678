import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleRegister } from "./impl"

export async function POST(req: NextRequest) {
  const { authBusinessLogic } = getBusinessLogics()
  const body = await req.json()
  const result = await implHandleRegister(body, authBusinessLogic, {})

  return NextResponse.json(result.body, { status: result.status })
}
