import { AuthBusinessLogicInterface } from "@/lib/repositories/auth"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { LoginSchema } from "@/lib/schemas/auth"
import { ERROR_CODES } from "@/app/api/error_codes"

export async function implHandleLogin(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
): Promise<{
  status: number
  body: ResponseWrapper<{
    token: string
    refresh_token: string
  } | null>
}> {
  try {
    const validationResult = LoginSchema.safeParse(body)

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", null, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const { email, password } = validationResult.data
    const result = await authBusinessLogic.login({ email, password })

    return {
      status: 200,
      body: new ResponseWrapper("success", result),
    }
  } catch (error: any) {
    console.error("Login error:", error)

    if (error.code === "INVALID_CREDENTIALS") {
      return {
        status: 401,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Invalid email or password"],
          [ERROR_CODES.INVALID_CREDENTIALS],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Login failed. Please try again."],
        [ERROR_CODES.LOGIN_FAILED],
      ),
    }
  }
}
