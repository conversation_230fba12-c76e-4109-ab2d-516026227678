import { AiBusinessLogic } from "./AIBusinessLogic"
import { AIEngineRunnerService } from "./AIEngineRunnerService"
import { AiExecutionDatasourceRepository } from "./mongodb"

import { getBusinessLogics } from "@/lib/repositories/businessLogics"

let _aiServices: ReturnType<typeof createAiServices> | null = null

export function getAiServices() {
  if (!_aiServices) {
    _aiServices = createAiServices()
  }
  return _aiServices
}

function createAiServices() {
  const {
    aiRulesBusinessLogic,
    messageTemplatesBusinessLogic,
    conversationMessagesBusinessLogic,
    datasourcesBusinessLogic,
    knowledgeBaseBusinessLogic,
  } = getBusinessLogics()

  const aiEngineRunner = new AIEngineRunnerService()
  const aiExecutionDatasourceRepo = new AiExecutionDatasourceRepository(
    conversationMessagesBusinessLogic,
  )

  const aiBusinessLogic = new AiBusinessLogic(
    aiEngineRunner,
    aiRulesBusinessLogic,
    messageTemplatesBusinessLogic,
    aiExecutionDatasourceRepo,
    datasourcesBusinessLogic,
    knowledgeBaseBusinessLogic,
  )

  return {
    aiEngineRunner,
    aiBusinessLogic,
  }
}
