import { AIInferenceEngine } from "./AIInferenceEngine"
import { ERROR_CODES } from "@/app/api/error_codes"

const AI_ENGINE_WEBHOOK_URL = process.env.AI_ENGINE_WEBHOOK_URL!
const AI_ENGINE_CALLBACK_AI_EXECUTION =
  process.env.AI_ENGINE_CALLBACK_AI_EXECUTION!!
const AI_ENGINE_CALLBACK_AI_WORKFLOW_EXECUTION =
  process.env.AI_ENGINE_CALLBACK_AI_WORKFLOW_EXECUTION!!

export class AIEngineRunnerService implements AIInferenceEngine {
  async execute(
    conversationId: string,
    executionId: string,
    context: string,
    payload: any,
  ): Promise<{
    status: "success" | "failed"
    data?: any
    errors?: string[]
    errorCodes?: string[]
  }> {
    try {
      const res = await fetch(`${AI_ENGINE_WEBHOOK_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Webhook-AI-Execution": AI_ENGINE_CALLBACK_AI_EXECUTION,
          "X-Webhook-AI-Workflow-Execution-Step":
            AI_ENGINE_CALLBACK_AI_WORKFLOW_EXECUTION,
          "X-Conversation-SessionId": conversationId,
          "X-AI-Workflow-Execution-ID": executionId,
        },
        body: JSON.stringify({ context, ...payload }),
      })

      if (!res.ok) {
        const text = await res.text()
        return {
          status: "failed",
          errors: [`n8n webhook error: ${res.status} - ${text}`],
          errorCodes: [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
        }
      }

      const data = await res.json()
      return {
        status: "success",
        data,
      }
    } catch (error: any) {
      return {
        status: "failed",
        errors: [error.message || "Unknown error calling n8n webhook"],
        errorCodes: [ERROR_CODES.EXTERNAL_SERVICE_ERROR],
      }
    }
  }
}
