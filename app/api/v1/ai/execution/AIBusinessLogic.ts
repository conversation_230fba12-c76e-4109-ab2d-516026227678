import { IDatasourceRepository } from "./repository-interface"
import { AIInferenceEngine } from "./AIInferenceEngine"
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules"
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates"
import { SessionContext } from "@/lib/repositories/auth/types"
import { KnowledgeBaseBusinessLogic } from "@/lib/repositories/knowledgeBase/BusinessLogic"
import { DatasourceBusinessLogic } from "@/lib/repositories/datasources"

export class AiBusinessLogic {
  constructor(
    private runner: AIInferenceEngine,
    private aiRuleBusinessLogic: AiRuleBusinessLogic,
    private messageTemplatesBusinessLogic: MessageTemplateBusinessLogic,
    private datasourceRepository: IDatasourceRepository,
    private dataSourceBusinessLogic: DatasourceBusinessLogic,
    private knowledgeBaseBusinessLogic: KnowledgeBaseBusinessLogic,
  ) {}

  async tryAnswer(
    conversationId: string,
    executionId: string,
    message: string,
    context: SessionContext,
  ) {
    const previous_messages = await this.datasourceRepository.getLastMessages(
      conversationId,
      10,
      context,
    )

    const [
      rulesResult,
      templatesResult,
      datasourceResult,
      knowledgeBaseResult,
    ] = await Promise.allSettled([
      this.aiRuleBusinessLogic.queryVectorDB(message, context, 3),
      this.messageTemplatesBusinessLogic.queryVectorDB(message, context, 3),
      this.dataSourceBusinessLogic.searchMatchingQuery(message, context, 3),
      this.knowledgeBaseBusinessLogic.searchSimilar(message, context, 3),
    ])

    const rules = rulesResult.status === "fulfilled" ? rulesResult.value : []
    const templates =
      templatesResult.status === "fulfilled" ? templatesResult.value : []
    const datasource =
      datasourceResult.status === "fulfilled" ? datasourceResult.value : []
    const knowledgeBase =
      knowledgeBaseResult.status === "fulfilled"
        ? knowledgeBaseResult.value
        : []

    const rags = [...datasource, ...knowledgeBase]

    await this.runner.execute(conversationId, executionId, "TRY_ANSWER", {
      message,
      previous_messages,
      data: { rules, templates, rags },
    })

    return { rules, templates, rags, previous_messages }
  }
}
