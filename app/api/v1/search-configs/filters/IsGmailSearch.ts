import { SearchConfigApiResponseVisitor } from "../SearchConfigApiResponseVisitor"
import { QueryValueHolder, QueryBuilder, SearchFilter } from "./base"

class GmailQueryValueHolder implements QueryValueHolder {
  constructor(
    private field: string,
    private isGmail: boolean,
  ) {}

  visit(queryBuilder: QueryBuilder) {
    const regex = /@gmail\.com$/i
    if (this.isGmail) {
      queryBuilder.addRegexFilter(this.field, regex)
    } else {
      queryBuilder.addNotRegexFilter(this.field, regex)
    }
  }
}

export class IsGmailSearch implements SearchFilter {
  readonly field = "is_gmail"
  readonly name = "isGmail"
  readonly label = "Is Gmail"

  private targetField = "email"

  buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
    visitor.addFilter({
      id: this.field,
      field: this.field,
      name: this.label,
      type: "boolean",
    })
  }

  parseFromQuery(
    query: URLSearchParams,
  ): { field: string; value: QueryValueHolder } | null {
    const value = query.get(this.field)
    if (value === null) return null

    if (value !== "true" && value !== "false") {
      throw new Error(
        `Invalid value for ${this.field}, expected "true" or "false"`,
      )
    }

    return {
      field: this.field,
      value: new GmailQueryValueHolder(this.targetField, value === "true"),
    }
  }
}
