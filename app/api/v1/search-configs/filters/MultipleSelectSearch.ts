import { SearchConfigApiResponseVisitor } from "../SearchConfigApiResponseVisitor"
import { QueryValueHolder, QueryBuilder, SearchFilter } from "./base"

export interface MultipleSelectOption {
  value: string
  label: string
}

class MultipleSelectQueryValueHolder implements QueryValueHolder {
  constructor(
    private field: string,
    private values: string[],
  ) {}

  visit(queryBuilder: QueryBuilder) {
    queryBuilder.addMultipleSelectFilter(this.field, this.values)
  }
}

export class MultipleSelectSearch implements SearchFilter {
  field: string
  name: string
  label: string
  options: MultipleSelectOption[]

  constructor(field: string, label: string, options: MultipleSelectOption[]) {
    this.field = field
    this.name = field
    this.label = label
    this.options = options
  }

  buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
    visitor.addFilter({
      id: this.field,
      field: this.field,
      name: this.label,
      type: "multiselect",
      options: this.options,
    })
  }

  parseFromQuery(
    query: URLSearchParams,
  ): { field: string; value: QueryValueHolder } | null {
    const rawValue = query.get(this.field)
    if (!rawValue) return null

    const values = rawValue.split(",").map((v) => v.trim())
    const validValues = new Set(this.options.map((o) => o.value))

    const invalidValues = values.filter((v) => !validValues.has(v))
    if (invalidValues.length > 0) {
      throw new Error(
        `Invalid choices for ${this.field}: ${invalidValues.join(", ")}`,
      )
    }

    return {
      field: this.field,
      value: new MultipleSelectQueryValueHolder(this.field, values),
    }
  }
}
