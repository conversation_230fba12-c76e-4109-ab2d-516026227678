import MESSAGE_KEYS from "@/app/api/message_keys"
import { BooleanSearch } from "../filters/BooleanSearch"
import { MultipleSelectSearch } from "../filters/MultipleSelectSearch"
import { StringSearch } from "../filters/StringSearch"
import { SearchConfigEntity } from "./interface"
import { ERROR_KEYS } from "./errors"
import {
  DateRangeSearchToday,
  DateRangeSearchYesterday,
  DateRangeSearchThisWeek,
  DateRangeSearchLastWeek,
  DateRangeSearchThisMonth,
  DateRangeSearchLastMonth,
  DateRangeSearchThisYear,
  DateRangeSearchLastYear,
} from "./common"
import { StringSort } from "../sort/StringSort"
import { BaseSearchConfig } from "./baseSearchConfig"

export class AiRuleSearchConfig
  extends BaseSearchConfig
  implements SearchConfigEntity
{
  constructor() {
    const filters = [
      new StringSearch("name", MESSAGE_KEYS.SEARCH_CONFIG.AI_RULE_NAME, {
        validations: [
          (value: string) => {
            if (value.length === 0)
              throw new Error(ERROR_KEYS.NAME_MUST_NOT_EMPTY)
            const SPECIAL_CHAR_REGEX = /[^\p{L}\p{N} ]/u
            if (SPECIAL_CHAR_REGEX.test(value)) {
              throw new Error(ERROR_KEYS.NAME_MUST_NOT_CONTAIN_SPECIAL_CHAR)
            }
          },
        ],
      }),
      new StringSearch(
        "conditions",
        MESSAGE_KEYS.SEARCH_CONFIG.AI_RULE_CONDITIONS,
        {
          validations: [
            (value: string) => {
              if (value.length === 0)
                throw new Error(ERROR_KEYS.NAME_MUST_NOT_EMPTY)
              const SPECIAL_CHAR_REGEX = /[^\p{L}\p{N} ]/u
              if (SPECIAL_CHAR_REGEX.test(value)) {
                throw new Error(ERROR_KEYS.NAME_MUST_NOT_CONTAIN_SPECIAL_CHAR)
              }
            },
          ],
        },
      ),

      new StringSearch("actions", MESSAGE_KEYS.SEARCH_CONFIG.AI_RULE_ACTIONS, {
        validations: [
          (value: string) => {
            if (value.length === 0)
              throw new Error(ERROR_KEYS.NAME_MUST_NOT_EMPTY)
            const SPECIAL_CHAR_REGEX = /[^\p{L}\p{N} ]/u
            if (SPECIAL_CHAR_REGEX.test(value)) {
              throw new Error(ERROR_KEYS.NAME_MUST_NOT_CONTAIN_SPECIAL_CHAR)
            }
          },
        ],
      }),

      new StringSearch(
        "description",
        MESSAGE_KEYS.SEARCH_CONFIG.AI_RULE_DESCRIPTION,
      ),
      new BooleanSearch(
        "isActive",
        MESSAGE_KEYS.SEARCH_CONFIG.AI_RULE_IS_ACTIVE,
      ),
      new MultipleSelectSearch(
        "tags",
        MESSAGE_KEYS.SEARCH_CONFIG.AI_RULE_TAGS,
        [],
      ),
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear,
    ]

    const searchableFields = ["name", "description", "tags"]

    const sort = [
      new StringSort("name", MESSAGE_KEYS.SEARCH_CONFIG.SORT_NAME),
      new StringSort("updatedAt", MESSAGE_KEYS.SEARCH_CONFIG.SORT_UPDATED_DATE),
    ]

    super(filters, sort, searchableFields, dateFilters)
  }
}

export const aiRuleSearchConfig = new AiRuleSearchConfig()
