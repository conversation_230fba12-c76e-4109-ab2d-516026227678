export class WorkflowMongoQueryBuilder {
  private query: Record<string, any> = {}

  getQuery() {
    return this.query
  }

  addDateRange(field: string, start: Date, end: Date) {
    this.query[field] = { $gte: start, $lte: end }
  }

  addStringFilter(field: string, value: string, operator?: string) {
    if (!value) return
    const escaped = this.escapeRegExp(value)
    switch (operator) {
      case "equals":
        this.query[field] = value
        break
      case "startsWith":
        this.query[field] = { $regex: `^${escaped}`, $options: "i" }
        break
      case "endsWith":
        this.query[field] = { $regex: `${escaped}$`, $options: "i" }
        break
      default:
        this.query[field] = { $regex: escaped, $options: "i" }
    }
  }

  addMultipleSelectFilter(field: string, values: string[]) {
    this.query[field] = { $in: values }
  }

  private escapeRegExp(input: string) {
    return input.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
  }
}

import MESSAGE_KEYS from "@/app/api/message_keys"
import { SearchConfigApiResponseVisitor } from "../SearchConfigApiResponseVisitor"
import { SearchFilter, type QueryValueHolder } from "../filters/base"
import { StringSearch } from "../filters/StringSearch"
import { MultipleSelectSearch } from "../filters/MultipleSelectSearch"
import { StringSort } from "../sort/StringSort"
import { Sort } from "../sort/interface"
import {
  DateRangeSearchToday,
  DateRangeSearchYesterday,
  DateRangeSearchThisWeek,
  DateRangeSearchLastWeek,
  DateRangeSearchThisMonth,
  DateRangeSearchLastMonth,
  DateRangeSearchThisYear,
  DateRangeSearchLastYear,
} from "./common"
import { SearchConfigEntity } from "./interface"

export class WorkflowExecutionsSearchConfig implements SearchConfigEntity {
  private filters: SearchFilter[]
  private sort: Sort[]
  private dateFilters: any[]
  searchableFields: string[]

  constructor() {
    this.filters = [
      new StringSearch("customerName", "Customer Name"),
      new StringSearch("originalMessage", "Original Message"),
      new StringSearch("id", "Execution ID"),

      new MultipleSelectSearch("finalStatus", "Final Status", [
        { value: "resolved", label: "Resolved" },
        { value: "escalated", label: "Escalated" },
        { value: "failed", label: "Failed" },
      ]),
    ]

    this.dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear,
    ]

    this.sort = [
      new StringSort("customerName", "Customer Name"),
      new StringSort("startTime", "Start Time"),
    ]

    this.searchableFields = ["customerName", "originalMessage", "id"]
  }

  buildForApiResponse(visitor: SearchConfigApiResponseVisitor) {
    this.filters.forEach((f) => f.buildForApiResponse(visitor))
    this.sort.forEach((s) => s.buildForApiResponse(visitor))
    this.dateFilters.forEach((d) => d.buildForApiResponse(visitor))
  }

  parseParams(searchParams: URLSearchParams): {
    result?: {
      sort: { field: string; direction: "ASC" | "DESC" }[]
      filters: { field: string; value: QueryValueHolder }[]
    }
    errors?: string[]
  } {
    try {
      const params: {
        sort: { field: string; direction: "ASC" | "DESC" }[]
        filters: { field: string; value: QueryValueHolder }[]
      } = { filters: [], sort: [] }

      for (const filter of this.filters) {
        const parsed = filter.parseFromQuery(searchParams)
        if (parsed) params.filters.push(parsed)
      }

      for (const dateFilter of this.dateFilters) {
        const parsed = dateFilter.parseFromQuery(searchParams)
        if (parsed) params.filters.push(parsed)
      }

      for (const sort of this.sort) {
        const parsed = sort.parseFromQuery(searchParams)
        if (parsed) params.sort.push(parsed)
      }

      return { result: params }
    } catch (err: any) {
      return { errors: [err.message] }
    }
  }

  buildMongoQuery(params: {
    sort: { field: string; direction: "ASC" | "DESC" }[]
    filters: { field: string; value: QueryValueHolder | any }[]
  }): { query: any; sort: any } {
    const mongoQueryBuilder = new WorkflowMongoQueryBuilder()

    for (const filter of params.filters) {
      const value = filter.value

      if (typeof value === "object" && "visit" in value) {
        value.visit(mongoQueryBuilder)
      } else if (Array.isArray(value)) {
        mongoQueryBuilder.addMultipleSelectFilter(filter.field, value)
      } else {
        mongoQueryBuilder.addStringFilter(
          filter.field,
          value?.toString?.() || "",
        )
      }
    }

    const query = mongoQueryBuilder.getQuery()
    const sort: Record<string, 1 | -1> = {}

    for (const s of params.sort) {
      sort[s.field] = s.direction === "ASC" ? 1 : -1
    }

    return { query, sort }
  }
}

export const workflowExecutionsSearchConfig =
  new WorkflowExecutionsSearchConfig()
