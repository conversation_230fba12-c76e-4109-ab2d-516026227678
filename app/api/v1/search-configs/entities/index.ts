import { SearchConfigApiResponseVisitor } from "../SearchConfigApiResponseVisitor"
import { aiRuleSearchConfig } from "./aiRule"
import { contactsSearchConfig } from "./contacts"
import { customerProfilesSearchConfig } from "./customerProfiles"
import { datasourcesSearchConfig } from "./datasources"
import { devicesSearchConfig } from "./devices"
import { SearchConfigEntity } from "./interface"
import { messageTemplatesSearchConfig } from "./messageTemplates"
import { workflowExecutionsSearchConfig } from "./workflowExecutions"

const entities: Record<string, SearchConfigEntity> = {
  contacts: contactsSearchConfig,
  workflowExecutions: workflowExecutionsSearchConfig,
  aiRules: aiRuleSearchConfig,
  messageTemplates: messageTemplatesSearchConfig,
  customerProfiles: customerProfilesSearchConfig,
  datasources: datasourcesSearchConfig,
  devices: devicesSearchConfig,
}

export function hasEntity(entityName: string): boolean {
  return entityName in entities
}

export function getRegisteredEntities(): string[] {
  return Object.keys(entities)
}

export function buildApiResponse(entityName: string): any {
  const entity = entities[entityName]
  const visitor = new SearchConfigApiResponseVisitor()

  entity.buildForApiResponse(visitor)

  return visitor.getResult()
}
