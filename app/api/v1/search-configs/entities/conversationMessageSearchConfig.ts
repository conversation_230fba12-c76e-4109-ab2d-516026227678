import MESSAGE_KEYS from "@/app/api/message_keys"
import { StringSort } from "../sort/StringSort"
import { BaseSearchConfig } from "./baseSearchConfig"
import { SearchConfigEntity } from "./interface"

export class ConversationMessageSearchConfig
  extends BaseSearchConfig
  implements SearchConfigEntity
{
  constructor() {
    const sort = [
      new StringSort("createdAt", MESSAGE_KEYS.SEARCH_CONFIG.SORT_CREATED_DATE),
    ]
    super([], sort, [], [])
  }
}

export const conversationMessagesSearchConfig =
  new ConversationMessageSearchConfig()
