import { BooleanSearch } from "../filters/BooleanSearch"
import { MultipleSelectSearch } from "../filters/MultipleSelectSearch"
import { StringSearch } from "../filters/StringSearch"
import { StringSort } from "../sort/StringSort"
import {
  DateRangeSearchLastMonth,
  DateRangeSearchLastWeek,
  DateRangeSearchLastYear,
  DateRangeSearchThisMonth,
  DateRangeSearchThisWeek,
  DateRangeSearchThisYear,
  DateRangeSearchToday,
  DateRangeSearchYesterday,
} from "./common"
import { SearchConfigEntity } from "./interface"
import { BaseSearchConfig } from "./baseSearchConfig"

export class DatasourcesSearchConfig
  extends BaseSearchConfig
  implements SearchConfigEntity
{
  constructor() {
    const filters = [
      new StringSearch("name", "Name"),
      new StringSearch("description", "Description"),
      new StringSearch("type", "Type"),
      new StringSearch("url", "URL"),

      new MultipleSelectSearch("status", "Status", [
        { value: "active", label: "Active" },
        { value: "inactive", label: "Inactive" },
        { value: "error", label: "Error" },
        { value: "pending", label: "Pending" },
      ]),

      new BooleanSearch("isActive", "Is Active"),
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear,
    ]

    const searchableFields = ["name", "description", "type", "url"]

    const sort = [
      new StringSort("name", "Name"),
      new StringSort("type", "Type"),
      new StringSort("status", "Status"),
      new StringSort("createdAt", "Created Date"),
      new StringSort("updatedAt", "Updated Date"),
    ]

    super(filters, sort, searchableFields, dateFilters)
  }
}

export const datasourcesSearchConfig = new DatasourcesSearchConfig()
