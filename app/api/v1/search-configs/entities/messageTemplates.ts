import { BooleanSearch } from "../filters/BooleanSearch"
import { MultipleSelectSearch } from "../filters/MultipleSelectSearch"
import { StringSearch } from "../filters/StringSearch"
import { StringSort } from "../sort/StringSort"
import {
  DateRangeSearchLastMonth,
  DateRangeSearchLastWeek,
  DateRangeSearchLastYear,
  DateRangeSearchThisMonth,
  DateRangeSearchThisWeek,
  DateRangeSearchThisYear,
  DateRangeSearchToday,
  DateRangeSearchYesterday,
} from "./common"
import { SearchConfigEntity } from "./interface"
import { BaseSearchConfig } from "./baseSearchConfig"

export class MessageTemplatesSearchConfig
  extends BaseSearchConfig
  implements SearchConfigEntity
{
  constructor() {
    const filters = [
      new StringSearch("title", "Title"),
      new StringSearch("query", "Query"),
      new StringSearch("template", "Template"),
      new StringSearch("category", "Category"),

      new MultipleSelectSearch("category", "Category", [
        { value: "greeting", label: "Greeting" },
        { value: "support", label: "Support" },
        { value: "sales", label: "Sales" },
        { value: "follow-up", label: "Follow-up" },
        { value: "closing", label: "Closing" },
      ]),

      new BooleanSearch("isActive", "Is Active"),
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear,
    ]

    const searchableFields = ["title", "query", "template", "category", "tags"]

    const sort = [
      new StringSort("title", "Title"),
      new StringSort("category", "Category"),
      new StringSort("createdAt", "Created Date"),
      new StringSort("updatedAt", "Updated Date"),
    ]

    super(filters, sort, searchableFields, dateFilters)
  }
}

export const messageTemplatesSearchConfig = new MessageTemplatesSearchConfig()
