export class MongoQueryBuilder {
  private query: Record<string, any> = {}

  // Internal stack to handle nested logical groups
  private groupStack: Record<string, any>[] = []

  constructor() {
    // Start with root query as current context
    this.groupStack.push(this.query)
  }

  getQuery() {
    return this.query
  }

  // Start a logical group ($and, $or, $not)
  startGroup(operator: "$and" | "$or" | "$not") {
    const group = operator === "$not" ? {} : []
    const current = this.groupStack[this.groupStack.length - 1]

    if (operator === "$not") {
      // $not expects a single object, so assign an object to current.$not
      current.$not = group
    } else {
      // $and/$or expect arrays
      if (!current[operator]) {
        current[operator] = []
      }
      current[operator].push(group)
    }

    // Push this new group to stack so subsequent filters add inside it
    this.groupStack.push(group)
  }

  // End the current logical group
  endGroup() {
    if (this.groupStack.length > 1) {
      this.groupStack.pop()
    } else {
      throw new Error("No group to end")
    }
  }

  // Add a field filter with common operators
  addFieldFilter(
    field: string,
    operator: "eq" | "ne" | "in" | "nin",
    value: any,
  ) {
    if (value === undefined || value === null) return

    const current = this.groupStack[this.groupStack.length - 1]

    switch (operator) {
      case "eq":
        // Equality: field: value
        this.mergeFilter(current, { [field]: value })
        break

      case "ne":
        // Not equal: field: { $ne: value }
        this.mergeFilter(current, { [field]: { $ne: value } })
        break

      case "in":
        // In array: field: { $in: [values] }
        if (Array.isArray(value)) {
          this.mergeFilter(current, { [field]: { $in: value } })
        }
        break

      case "nin":
        // Not in array: field: { $nin: [values] }
        if (Array.isArray(value)) {
          this.mergeFilter(current, { [field]: { $nin: value } })
        }
        break
    }
  }

  // Helper to merge filter into current query group without overwriting existing keys
  private mergeFilter(
    target: Record<string, any>,
    filter: Record<string, any>,
  ) {
    for (const key in filter) {
      if (key in target) {
        // If key exists, merge conditions
        if (
          typeof target[key] === "object" &&
          typeof filter[key] === "object" &&
          !Array.isArray(target[key]) &&
          !Array.isArray(filter[key])
        ) {
          target[key] = { ...target[key], ...filter[key] }
        } else {
          // Otherwise, overwrite (or could be customized)
          target[key] = filter[key]
        }
      } else {
        target[key] = filter[key]
      }
    }
  }

  addDateRange(field: string, start: Date, end: Date) {
    this.mergeFilter(this.groupStack[this.groupStack.length - 1], {
      [field]: { $gte: start, $lte: end },
    })
  }

  addStringFilter(field: string, value: string, operator?: string) {
    if (!value) return

    const escapedValue = this.escapeRegExp(value)

    let condition
    switch (operator) {
      case "equals":
        condition = value
        break
      case "startsWith":
        condition = { $regex: `^${escapedValue}`, $options: "i" }
        break
      case "endsWith":
        condition = { $regex: `${escapedValue}$`, $options: "i" }
        break
      case "contains":
      default:
        condition = { $regex: escapedValue, $options: "i" }
        break
    }

    this.mergeFilter(this.groupStack[this.groupStack.length - 1], {
      [field]: condition,
    })
  }

  private escapeRegExp(input: string): string {
    return input.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
  }

  addBooleanFilter(field: string, value: boolean) {
    this.mergeFilter(this.groupStack[this.groupStack.length - 1], {
      [field]: value,
    })
  }

  addMultipleSelectFilter(field: string, values: string[]) {
    this.mergeFilter(this.groupStack[this.groupStack.length - 1], {
      [field]: { $in: values },
    })
  }

  addRegexFilter(field: string, regex: RegExp) {
    this.mergeFilter(this.groupStack[this.groupStack.length - 1], {
      [field]: regex,
    })
  }

  addNotRegexFilter(field: string, regex: RegExp) {
    this.mergeFilter(this.groupStack[this.groupStack.length - 1], {
      [field]: { $not: regex },
    })
  }
}
