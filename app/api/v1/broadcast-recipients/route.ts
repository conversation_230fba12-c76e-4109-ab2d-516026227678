import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { z } from "zod"
import { buildSessionContext } from "../../sharedFunction"

// Validation schema for broadcast recipients query
const BroadcastRecipientsQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  search: z.string().optional(),
  tags: z.array(z.string()).optional(), // Array of tags to include
  excludedIds: z.array(z.string()).optional(), // Array of contact IDs to exclude
  includeDeleted: z.boolean().default(false),
})

export interface BroadcastRecipientContact {
  id: string
  name: string
  phone: string
  email?: string
  tags: string[] // Always array, never undefined
}

export interface TagWithCount {
  tag: string
  count: number
}

export interface BroadcastRecipientsResponse {
  contacts: BroadcastRecipientContact[]
  total: number
  page: number
  limit: number
  totalPages: number
  availableTags: TagWithCount[] // All unique tags with contact counts
  selectedCount: number // Number of contacts that would be selected
}

// GET /api/v1/broadcast-recipients - Get contacts for broadcast with filtering
export async function GET(req: NextRequest) {
  const { contactsBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { searchParams } = new URL(req.url)

    // Parse query parameters
    const queryData = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "20"),
      search: searchParams.get("search") || undefined,
      tags: searchParams.get("tags")
        ? searchParams.get("tags")!.split(",")
        : undefined,
      excludedIds: searchParams.get("excludedIds")
        ? searchParams.get("excludedIds")!.split(",")
        : undefined,
      includeDeleted: searchParams.get("includeDeleted") === "true",
    }

    const validationResult = BroadcastRecipientsQuerySchema.safeParse(queryData)
    if (!validationResult.success) {
      return NextResponse.json(
        ResponseBuilder.fail(
          validationResult.error.errors.map((e) => e.message),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    const { page, limit, search, tags, excludedIds, includeDeleted } =
      validationResult.data

    // Build filters for contact query
    const filters: any[] = []

    // Add tag filters if specified
    if (tags && tags.length > 0) {
      filters.push({
        field: "tags",
        value: { $in: tags }, // MongoDB query to match any of the specified tags
      })
    }

    // Exclude specific contact IDs if specified
    if (excludedIds && excludedIds.length > 0) {
      filters.push({
        field: "id",
        value: { $nin: excludedIds }, // MongoDB query to exclude these IDs
      })
    }

    // Get contacts based on filters
    const contactsResult = await contactsBusinessLogic.getAll(
      {
        page,
        limit,
        search,
        filters,
        includeDeleted,
        sort: [{ field: "name", direction: "ASC" }],
      },
      context,
    )

    // Get all contacts to extract unique tags (for the tag selector)
    const allContactsResult = await contactsBusinessLogic.getAll(
      {
        page: 1,
        limit: 1000, // Get a large number to extract all tags
        includeDeleted: false,
      },
      context,
    )

    // Extract unique tags with counts from all contacts
    const tagCounts = new Map<string, number>()

    allContactsResult.items.forEach((contact) => {
      if (contact.tags && contact.tags.length > 0) {
        contact.tags.forEach((tag) => {
          if (tag && tag.trim() !== "") {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
          }
        })
      }
    })

    const availableTags: TagWithCount[] = Array.from(tagCounts.entries())
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => a.tag.localeCompare(b.tag))

    // Calculate how many contacts would be selected with current filters
    const selectedContactsResult = await contactsBusinessLogic.getAll(
      {
        page: 1,
        limit: 1, // We only need the count
        search,
        filters,
        includeDeleted,
      },
      context,
    )

    // Transform contacts to broadcast recipient format
    const broadcastContacts: BroadcastRecipientContact[] =
      contactsResult.items.map((contact) => ({
        id: contact.id,
        name: contact.name,
        phone: contact.phone,
        email: contact.email,
        tags: contact.tags || [],
      }))

    const totalPages = Math.ceil(contactsResult.total / limit)

    const responseData: BroadcastRecipientsResponse = {
      contacts: broadcastContacts,
      total: contactsResult.total,
      page,
      limit,
      totalPages,
      availableTags,
      selectedCount: selectedContactsResult.total,
    }

    return NextResponse.json(ResponseBuilder.success(responseData), {
      status: 200,
    })
  } catch (error) {
    console.error("Broadcast recipients GET route error:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["api.error.internal_server_error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
