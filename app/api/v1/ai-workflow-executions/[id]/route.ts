import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { NextRequest, NextResponse } from "next/server"
import {
  implHandleGetAiWorkflowExecution,
  implHandleUpdateAiWorkflowExecution,
  implHandleDeleteAiWorkflowExecution,
} from "../impl"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  const { workflowExecutionsBusinessLogic } = getBusinessLogics()
  try {
    const { context: sessionContext, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await context.params
    const result = await implHandleGetAiWorkflowExecution(
      id,
      workflowExecutionsBusinessLogic,
      sessionContext,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AiWorkflowExecution GET route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  const { workflowExecutionsBusinessLogic } = getBusinessLogics()
  try {
    const { context: sessionContext, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await context.params
    const body = await req.json()
    const result = await implHandleUpdateAiWorkflowExecution(
      id,
      body,
      workflowExecutionsBusinessLogic,
      sessionContext,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AiWorkflowExecution PUT route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ id: string }> },
) {
  const { workflowExecutionsBusinessLogic } = getBusinessLogics()
  try {
    const { context: sessionContext, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await context.params
    const result = await implHandleDeleteAiWorkflowExecution(
      id,
      workflowExecutionsBusinessLogic,
      sessionContext,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("AiWorkflowExecution DELETE route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
