import { AiWorkflowExecution } from "@/lib/repositories/aiWorkflowExecutions/interface"

// Common types used across workflowExecution implementations
export interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

export interface AiWorkflowExecutionsStatsData {
  totalAiWorkflowExecutions: number
  activeAiWorkflowExecutions: number
  deletedAiWorkflowExecutions: number
  workflowExecutionsWithEmail: number
  workflowExecutionsWithTags: number
  recentAiWorkflowExecutions: number // last 7 days
  statusBreakdown: Array<{
    status: string
    count: number
    percentage: number
  }>
  tagBreakdown: Array<{
    tag: string
    count: number
    percentage: number
  }>
  createdByBreakdown: Array<{
    createdBy: string
    count: number
    percentage: number
  }>
  dailyStats: Array<{
    date: string
    created: number
    deleted: number
  }>
}

// ============================================================================
// QUERY PARAMETERS TYPES
// ============================================================================

export interface AiWorkflowExecutionQueryParams {
  search?: string
  includeDeleted?: boolean
  page?: number
  limit?: number
  sort?: {
    field: keyof AiWorkflowExecution | string
    direction: "ASC" | "DESC"
  }[]
  filters?: {
    field: keyof AiWorkflowExecution | string
    value: AiWorkflowExecution[keyof AiWorkflowExecution] | any
  }[]
}

export interface AiWorkflowExecutionStatsParams {
  search?: string
  includeDeleted?: boolean
  filters?: {
    field: string
    value: any
  }[]
  dateFrom?: string
  dateTo?: string
}
