import { AiWorkflowExecutionBusinessLogicInterface } from "@/lib/repositories/aiWorkflowExecutions"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { AiWorkflowExecutionUpdateSchema } from "@/lib/validations/workflowExecution"

// Update AiWorkflowExecution Implementation
export async function implHandleUpdateAiWorkflowExecution(
  id: string,
  data: any,
  businessLogic: AiWorkflowExecutionBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = AiWorkflowExecutionUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const workflowExecutions = await businessLogic.update(
      id,
      validationResult.data,
      context,
    )

    if (!workflowExecutions) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["AiWorkflowExecution not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", workflowExecutions),
    }
  } catch (error: any) {
    console.error("Update workflowExecutions error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_UPDATE_DATA") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    if (error.code === "INVALID_EMAIL" || error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update workflowExecutions. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}
