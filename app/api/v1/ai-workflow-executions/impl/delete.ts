import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { AiWorkflowExecutionBusinessLogicInterface } from "@/lib/repositories/aiWorkflowExecutions"
import { SessionContext } from "@/lib/repositories/auth/types"

// Delete AiWorkflowExecution Implementation
export async function implHandleDeleteAiWorkflowExecution(
  id: string,
  businessLogic: AiWorkflowExecutionBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["AiWorkflowExecution not found or access denied"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "AiWorkflowExecution deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete workflowExecutions error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete workflowExecutions. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}
