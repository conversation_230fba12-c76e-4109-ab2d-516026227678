import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetContactGroupContacts,
  implHandleUpdateContactGroupContacts,
} from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { buildSessionContext } from "../../../../sharedFunction"
import { contactGroupAccessManager } from "@/lib/repositories/accessManagers"
import { parseSearchParams } from "../../../../parseSearchParams"
import { contactsSearchConfig } from "../../../search-configs/entities/contacts"

export async function GET(
  req: NextRequest,
  ctx: { params: Promise<{ id: string }> },
) {
  const {
    contactGroupsBusinessLogic,
    contactGroupContactsBusinessLogic,
    contactsBusinessLogic,
  } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await ctx.params

    // Parse search parameters for pagination and filtering
    const { searchParams } = new URL(req.url)
    const { params, response: parsedResponse } = parseSearchParams(
      searchParams,
      contactsSearchConfig,
    )
    if (parsedResponse) {
      return parsedResponse
    }

    const result = await implHandleGetContactGroupContacts(
      id,
      params,
      contactGroupsBusinessLogic,
      contactGroupContactsBusinessLogic,
      contactsBusinessLogic,
      context,
      contactGroupAccessManager(context.organization?.id || context.user.id),
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Contact Group Contacts GET route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  ctx: { params: Promise<{ id: string }> },
) {
  const { contactGroupsBusinessLogic, contactGroupContactsBusinessLogic } =
    getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await ctx.params
    const body = await req.json()

    const result = await implHandleUpdateContactGroupContacts(
      id,
      body,
      contactGroupsBusinessLogic,
      contactGroupContactsBusinessLogic,
      context,
      contactGroupAccessManager(context.organization?.id || context.user.id),
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Contact Group Contacts PUT route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        { success: false, addedCount: 0, deletedCount: 0 },
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
