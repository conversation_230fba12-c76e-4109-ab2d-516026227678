import { ContactGroupBusinessLogicInterface } from "@/lib/repositories/contactGroups/interface"
import { ContactGroupContactBusinessLogicInterface } from "@/lib/repositories/contactGroupContacts/interface"
import {
  ContactBusinessLogicInterface,
  Contact,
  ContactQueryParams,
} from "@/lib/repositories/contacts/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { GetAllResultPaginated } from "../../impl/types"
import {
  ContactGroupResponses,
  createSuccessResponse,
} from "@/lib/utils/api-response-helper"
import { SessionContext } from "@/lib/repositories/auth/types"
import { GroupRoleResourceAccessManager } from "@/lib/repositories/AccessManager"
import { ContactGroupAccessResource } from "../../impl/access_manager_resource"

// Get Contacts in Contact Group Implementation
export async function implHandleGetContactGroupContacts(
  contactGroupId: string,
  params: ContactQueryParams,
  contactGroupBusinessLogic: ContactGroupBusinessLogicInterface,
  contactGroupContactsBusinessLogic: ContactGroupContactBusinessLogicInterface,
  contactsBusinessLogic: ContactBusinessLogicInterface,
  context: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<ContactGroupAccessResource>,
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<Contact>>
}> {
  try {
    // First, verify the contact group exists
    const contactGroup = await contactGroupBusinessLogic.getById(
      contactGroupId,
      context,
      false, // Don't include deleted
    )

    if (!contactGroup) {
      return ContactGroupResponses.notFound()
    }

    // Get contact group relationships with pagination
    const contactGroupContactsParams = {
      page: params.page,
      limit: params.limit,
      search: params.search,
      filters: params.filters,
    }

    const contactGroupContacts =
      await contactGroupContactsBusinessLogic.getByContactGroupId(
        contactGroupId,
        context,
        contactGroupContactsParams,
      )

    // If no contacts in the group, return empty result
    if (contactGroupContacts.items.length === 0) {
      return createSuccessResponse<GetAllResultPaginated<Contact>>({
        items: [],
        total: 0,
        page: params.page || 1,
      })
    }

    // Extract contact IDs from the relationships
    const contactIds = contactGroupContacts.items.map((rel) => rel.contactId)

    // Build query parameters to get contacts by IDs
    const contactQueryParams: ContactQueryParams = {
      page: 1,
      limit: contactIds.length,
      filterEnhanced: {
        AND: [{ field: "id", operator: "IN", value: contactIds }],
      },
    }

    // Get the contacts using the contacts business logic
    const contactsResult = await contactsBusinessLogic.getAll(
      contactQueryParams,
      context,
    )

    return createSuccessResponse<GetAllResultPaginated<Contact>>({
      items: contactsResult.items,
      total: contactGroupContacts.total,
      page: params.page || 1,
    })
  } catch (error: any) {
    console.error("Get contact group contacts error:", error)

    return ContactGroupResponses.fetchFailed()
  }
}

// Update Contact Group Contacts Implementation
export async function implHandleUpdateContactGroupContacts(
  contactGroupId: string,
  body: { added: string[]; deleted: string[] },
  contactGroupBusinessLogic: ContactGroupBusinessLogicInterface,
  contactGroupContactsBusinessLogic: ContactGroupContactBusinessLogicInterface,
  context: SessionContext,
  accessManager?: GroupRoleResourceAccessManager<ContactGroupAccessResource>,
): Promise<{
  status: number
  body: ResponseWrapper<{
    success: boolean
    addedCount: number
    deletedCount: number
  }>
}> {
  try {
    const { added = [], deleted = [] } = body

    // Validate input
    if (!Array.isArray(added) || !Array.isArray(deleted)) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          { success: false, addedCount: 0, deletedCount: 0 },
          ["Added and deleted must be arrays"],
          ["INVALID_INPUT"],
        ),
      }
    }

    // First, verify the contact group exists
    const contactGroup = await contactGroupBusinessLogic.getById(
      contactGroupId,
      context,
      false, // Don't include deleted
    )

    if (!contactGroup) {
      return ContactGroupResponses.notFound()
    }

    let addedCount = 0
    let deletedCount = 0

    // Process additions
    for (const contactId of added) {
      if (typeof contactId === "string" && contactId.trim()) {
        try {
          const existing =
            await contactGroupContactsBusinessLogic.getByContactIdAndContactGroupId(
              contactId.trim(),
              contactGroupId,
              context,
            )
          if (!existing) {
            await contactGroupContactsBusinessLogic.create(
              {
                contactGroupId,
                contactId: contactId.trim(),
              },
              context,
            )
            addedCount++
          }
        } catch (error) {
          // Ignore duplicate errors, but log other errors
          console.warn(
            `Failed to add contact ${contactId} to group ${contactGroupId}:`,
            error,
          )
        }
      }
    }

    // Process deletions
    for (const contactId of deleted) {
      if (typeof contactId === "string" && contactId.trim()) {
        try {
          const existing =
            await contactGroupContactsBusinessLogic.getByContactIdAndContactGroupId(
              contactId.trim(),
              contactGroupId,
              context,
            )
          if (!existing) {
            continue
          }

          await contactGroupContactsBusinessLogic.delete(existing.id, context)
          deletedCount++
        } catch (error) {
          console.warn(
            `Failed to remove contact ${contactId} from group ${contactGroupId}:`,
            error,
          )
        }
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        success: true,
        addedCount,
        deletedCount,
      }),
    }
  } catch (error: any) {
    console.error("Update contact group contacts error:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        { success: false, addedCount: 0, deletedCount: 0 },
        ["Failed to update contact group contacts"],
        ["UPDATE_FAILED"],
      ),
    }
  }
}
