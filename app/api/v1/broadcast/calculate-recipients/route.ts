import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { z } from "zod"
import { buildSessionContext } from "../../../sharedFunction"

// Validation schema for calculate recipients request
const CalculateRecipientsSchema = z.object({
  selectedTags: z.array(z.string()).optional().default([]),
  excludedContactIds: z.array(z.string()).optional().default([]),
  includedContactIds: z.array(z.string()).optional().default([]),
})

export interface CalculateRecipientsResponse {
  finalCount: number
  totalContacts: number
  selectedByTags: number
  excludedCount: number
  includedCount: number
  breakdown: {
    taggedContacts: number
    excludedFromTagged: number
    manuallyIncluded: number
    manuallyExcluded: number
  }
}

// POST /api/v1/broadcast/calculate-recipients - Calculate recipient count
export async function POST(req: NextRequest) {
  const { broadcastRecipientBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const validationResult = CalculateRecipientsSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        ResponseBuilder.fail(
          [
            "Invalid request parameters",
            ...validationResult.error.errors.map((e) => e.message),
          ],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    const { selectedTags, excludedContactIds, includedContactIds } =
      validationResult.data

    // Use the existing business logic method to calculate recipients
    const broadcastData = {
      recipientTags: selectedTags.length > 0 ? selectedTags : undefined,
      excludedRecipientIds:
        excludedContactIds.length > 0 ? excludedContactIds : undefined,
      manualSelectedTargetRecipients:
        includedContactIds.length > 0 ? includedContactIds : undefined,
    }

    const finalCount =
      await broadcastRecipientBusinessLogic.countEstimatedTotalRecipientForBroadcastData(
        broadcastData,
        context,
      )

    // Calculate additional breakdown information for better UX
    let totalContacts = 0
    let selectedByTags = 0
    let taggedContacts = 0

    // Get total contacts count
    const totalContactsData =
      await broadcastRecipientBusinessLogic.countEstimatedTotalRecipientForBroadcastData(
        {}, // No filters = all contacts
        context,
      )
    totalContacts = totalContactsData

    // If tags are selected, get count of contacts with those tags (before exclusions)
    if (selectedTags.length > 0) {
      const taggedContactsData =
        await broadcastRecipientBusinessLogic.countEstimatedTotalRecipientForBroadcastData(
          { recipientTags: selectedTags },
          context,
        )
      taggedContacts = taggedContactsData
      selectedByTags = taggedContacts
    } else {
      selectedByTags = totalContacts
    }

    const responseData: CalculateRecipientsResponse = {
      finalCount,
      totalContacts,
      selectedByTags,
      excludedCount: excludedContactIds.length,
      includedCount: includedContactIds.length,
      breakdown: {
        taggedContacts,
        excludedFromTagged: Math.max(
          0,
          selectedByTags - finalCount - includedContactIds.length,
        ),
        manuallyIncluded: includedContactIds.length,
        manuallyExcluded: excludedContactIds.length,
      },
    }

    return NextResponse.json(ResponseBuilder.success(responseData), {
      status: 200,
    })
  } catch (error) {
    console.error("Error calculating recipients:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["Failed to calculate recipients"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
