import { NextRequest } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { BroadcastRecipientStatus } from "@/lib/repositories/broadcast/interface"
import { BroadcastSendMessage } from "@/lib/queue/MessageQueue"
import { SessionContext } from "@/lib/repositories/auth/types"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { broadcastProviders } from "@/lib/providers"

export async function implHandleBroadcastSend(
  body: BroadcastSendMessage,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  const {
    broadcastBusinessLogic,
    broadcastRecipientBusinessLogic,
    contactsBusinessLogic,
    devicesBusinessLogic,
  } = getBusinessLogics()

  if (body.type !== "broadcast.send") {
    return {
      status: 400,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Invalid message type"],
        ["INVALID_MESSAGE_TYPE"],
      ),
    }
  }

  const { payload, context: bodyContext } = body
  const { broadcastId, broadcastRecipientId } = payload

  const context: SessionContext = {
    user: {
      id: bodyContext.userId,
      name: "",
      email: "",
    },
    organization: bodyContext.organizationId
      ? { id: bodyContext.organizationId }
      : undefined,
  }

  if (!broadcastId || !broadcastRecipientId) {
    return {
      status: 400,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Missing required fields"],
        ["MISSING_REQUIRED_FIELDS"],
      ),
    }
  }

  const broadcast = await broadcastBusinessLogic.getById(broadcastId, context)
  if (!broadcast) {
    return {
      status: 400,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Broadcast not found"],
        ["BROADCAST_NOT_FOUND"],
      ),
    }
  }

  const recipient = await broadcastRecipientBusinessLogic.getById(
    broadcastRecipientId,
    context,
  )
  if (!recipient) {
    return {
      status: 400,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Broadcast recipient not found"],
        ["BROADCAST_RECIPIENT_NOT_FOUND"],
      ),
    }
  }

  const contact = await contactsBusinessLogic.getById(
    recipient.contactId,
    context,
  )
  if (!contact) {
    return {
      status: 400,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Contact not found"],
        ["CONTACT_NOT_FOUND"],
      ),
    }
  }

  const device = await devicesBusinessLogic.getById(broadcast.deviceId, context)
  if (!device) {
    return {
      status: 400,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Device not found"],
        ["DEVICE_NOT_FOUND"],
      ),
    }
  }

  const provider = broadcastProviders[process.env.BROADCAST_PROVIDER!]
  if (!provider) {
    console.error("Provider not available")

    await broadcastRecipientBusinessLogic.update(
      broadcastRecipientId,
      {
        status: BroadcastRecipientStatus.FAILED,
        failedAt: new Date(),
        errorMessage: "Provider not available",
      },
      context,
    )

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Provider not available"],
        ["PROVIDER_NOT_AVAILABLE"],
      ),
    }
  }

  try {
    await provider.sendBroadcast(
      contact.phone,
      broadcast.message,
      device.sessionId,
    )

    await broadcastRecipientBusinessLogic.update(
      broadcastRecipientId,
      {
        status: BroadcastRecipientStatus.SENT,
        sentAt: new Date(),
      },
      context,
    )

    console.log(
      `Message sent successfully for broadcast ${broadcastId}, recipient ${broadcastRecipientId}`,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        broadcastId,
        broadcastRecipientId,
        contactId: contact.id,
        status: "sent",
      }),
    }
  } catch (error: any) {
    console.error(
      `Failed to send message for broadcast ${broadcastId}, recipient ${broadcastRecipientId}:`,
      error,
    )

    await broadcastRecipientBusinessLogic.update(
      broadcastRecipientId,
      {
        status: BroadcastRecipientStatus.FAILED,
        failedAt: new Date(),
        errorMessage: error.message || "Failed to send message",
      },
      context,
    )

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to send message"],
        ["FAILED_TO_SEND_MESSAGE"],
      ),
    }
  }
}
