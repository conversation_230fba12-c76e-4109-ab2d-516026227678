import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleGetAllBroadcasts, implHandleCreateBroadcast } from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

// GET /api/v1/broadcast - Get all broadcasts
export async function GET(req: NextRequest) {
  const { broadcastBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { searchParams } = new URL(req.url)

    // Parse query parameters
    const params = {
      search: searchParams.get("search") || undefined,
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("per_page") || "10"),
      includeDeleted: searchParams.get("includeDeleted") === "true",
      status: searchParams.get("status") || undefined,
      createdBy: searchParams.get("createdBy") || undefined,
      dateFrom: searchParams.get("dateFrom") || undefined,
      dateTo: searchParams.get("dateTo") || undefined,
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "DESC",
    }

    const result = await implHandleGetAllBroadcasts(
      broadcastBusinessLogic,
      context,
      params,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in GET /api/v1/broadcast:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

// POST /api/v1/broadcast - Create broadcast
export async function POST(req: NextRequest) {
  const { broadcastBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const result = await implHandleCreateBroadcast(
      body,
      broadcastBusinessLogic,
      context,
    )

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in POST /api/v1/broadcast:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: undefined,
        messages: [error.message || "Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
