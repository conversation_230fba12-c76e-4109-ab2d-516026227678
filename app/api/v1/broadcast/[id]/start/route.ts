import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseBuilder } from "@/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"
import { implHandleStartBroadcast } from "../../impl"

// POST /api/v1/broadcast/[id]/start - Start a broadcast
export async function POST(
  req: NextRequest,
  routeContext: { params: Promise<{ id: string }> },
) {
  const { broadcastBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { id } = await routeContext.params
    const result = await implHandleStartBroadcast(
      id,
      broadcastBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Error in POST /api/v1/broadcast/[id]/start:", error)
    return NextResponse.json(
      ResponseBuilder.fail(
        ["api.error.internal_server_error"],
        ["ERROR_INTERNAL_SERVER_ERROR"],
      ),
      { status: 500 },
    )
  }
}
