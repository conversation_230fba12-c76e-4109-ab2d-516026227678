import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleUpdateConversation,
  implHandleDeleteConversation,
  implHandleGetConversation,
} from "../impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"

export async function GET(
  req: NextRequest,
  routeContext: { params: Promise<{ conversationId: string }> },
) {
  const { conversationBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }
    const { conversationId } = await routeContext.params
    const result = await implHandleGetConversation(
      conversationId,
      conversationBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Conversation GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function PUT(
  req: NextRequest,
  routeContext: { params: Promise<{ conversationId: string }> },
) {
  const { conversationBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { conversationId } = await routeContext.params
    const body = await req.json()
    const result = await implHandleUpdateConversation(
      conversationId,
      body,
      conversationBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Conversation PUT route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}

export async function DELETE(
  req: NextRequest,
  routeContext: { params: Promise<{ conversationId: string }> },
) {
  const { conversationBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { conversationId } = await routeContext.params
    const result = await implHandleDeleteConversation(
      conversationId,
      conversationBusinessLogic,
      context,
    )
    return NextResponse.json(result.body, { status: result.status })
  } catch (error) {
    console.error("Conversation DELETE route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
