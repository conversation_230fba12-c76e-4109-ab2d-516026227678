import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"
import { getSessionToConversationStore } from "../../../functions/webhook/SessionToConversationRoomStore"
import { handleAiWorkflow } from "../../../functions/webhook/impl"

export async function POST(
  req: NextRequest,
  routeContext: { params: Promise<{ conversationId: string }> },
) {
  const {
    conversationBusinessLogic,
    conversationMessagesBusinessLogic,
    workflowExecutionsBusinessLogic,
  } = getBusinessLogics()
  const { aiBusinessLogic } = getAiServices()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) return response

    const messageId = req.nextUrl.searchParams.get("messageId")
    if (!messageId) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Missing messageId in query"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    const message = await conversationMessagesBusinessLogic.getById(
      messageId,
      context,
    )
    if (!message) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Message not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    const { conversationId } = await routeContext.params
    const session =
      await getSessionToConversationStore().getByConversationId(conversationId)
    if (!session) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["No session found for conversation"],
          [ERROR_CODES.NOT_FOUND],
        ),
        { status: 404 },
      )
    }

    const workflow = await handleAiWorkflow(
      conversationId,
      {
        from: message.senderId,
        body: message.content,
      },
      context,
      session.session,
      conversationBusinessLogic,
      workflowExecutionsBusinessLogic,
      aiBusinessLogic,
    )

    if (!workflow) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          undefined,
          ["Failed to generate AI response"],
          [ERROR_CODES.INTERNAL_SERVER_ERROR],
        ),
        { status: 500 },
      )
    }

    return NextResponse.json(
      new ResponseWrapper("success", { executionId: workflow.executionId }),
    )
  } catch (error: any) {
    console.error("AI answer route error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to generate AI response"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR],
      ),
      { status: 500 },
    )
  }
}
