import {
  ConversationBusinessLogicInterface,
  Conversation,
  ConversationCreateInput,
  ConversationUpdateInput,
} from "@/lib/repositories/conversations/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  ConversationCreateSchema,
  ConversationUpdateSchema,
} from "@/lib/validations/conversation"
import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { validateParamsAndTrim } from "../../validateInputParams"
import {
  RealtimeConversationList,
  RealtimeConversationRoom,
} from "@/lib/realtime/model"

async function sendConversationRealtimeUpdates(
  conversation: Conversation,
  context: SessionContext,
) {
  try {
    const enriched = await enrichParticipantInfo(context, [conversation])
    await RealtimeConversationList.UPDATE_CONVERSATION(enriched[0]).send()
    await RealtimeConversationRoom.UPDATE_CONVERSATION(enriched[0]).send()
  } catch (error) {
    console.error("Error sending realtime updates:", error)
  }
}

// Create Conversation Implementation
export async function implHandleCreateConversation(
  data: any,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = ConversationCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    // Add context fields to the validated data
    const conversationData: ConversationCreateInput = {
      ...validationResult.data,
    }

    const conversation = await businessLogic.create(conversationData, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", conversation),
    }
  } catch (error: any) {
    console.error("Create conversation error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create conversation. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Update Conversation Implementation
export async function implHandleUpdateConversation(
  id: string,
  data: any,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate input data
    const validationResult = ConversationUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const conversation = await businessLogic.update(
      id,
      validationResult.data,
      context,
    )

    if (!conversation) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    await sendConversationRealtimeUpdates(conversation, context)

    return {
      status: 200,
      body: new ResponseWrapper("success", conversation),
    }
  } catch (error: any) {
    console.error("Update conversation error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update conversation. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Delete Conversation Implementation
export async function implHandleDeleteConversation(
  id: string,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Conversation deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete conversation error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete conversation. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Get Conversation by ID Implementation
export async function implHandleGetConversation(
  id: string,
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const conversation = await businessLogic.getById(
      id,
      context,
      includeDeleted,
    )

    if (!conversation) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Conversation not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", conversation),
    }
  } catch (error: any) {
    console.error("Get conversation error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch conversation. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

// Unified Get All Conversations Handler with Optimized Participant Resolution
export async function implHandleGetAllConversations(
  businessLogic: ConversationBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof Conversation | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof Conversation | string
      value: Conversation[keyof Conversation] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<Conversation> | undefined>
}> {
  try {
    const queryParams = validateParamsAndTrim(params)

    const result = await businessLogic.getAll(queryParams, context)
    const conversations = result.items

    const enrichedConversations = await enrichParticipantInfo(
      context,
      conversations,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        ...result,
        items: enrichedConversations,
        page: queryParams.page,
      }),
    }
  } catch (error: any) {
    console.error("Get conversation error:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch conversation. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

export async function enrichParticipantInfo(
  context: SessionContext,
  conversations: Conversation[],
) {
  const { contactsBusinessLogic, usersBusinessLogic } = getBusinessLogics()
  const participantCache = new Map<string, { id: string; name: string }>()

  const resolveParticipant = async (participantId: string) => {
    if (participantCache.has(participantId)) {
      return participantCache.get(participantId)!
    }

    const [contact, user] = await Promise.all([
      contactsBusinessLogic.getById(participantId, context),
      usersBusinessLogic.getById(participantId, context),
    ])

    const name = contact?.name || user?.name || participantId
    const participant = { id: participantId, name }

    participantCache.set(participantId, participant)
    return participant
  }

  const enrichedConversations = await Promise.all(
    conversations.map(async (conversation) => {
      const participantsFull = await Promise.all(
        conversation.participants.map(resolveParticipant),
      )

      return {
        ...conversation,
        participantsFull,
      }
    }),
  )
  return enrichedConversations
}
