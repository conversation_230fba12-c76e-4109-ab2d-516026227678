import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ERROR_CODES } from "@/app/api/error_codes"

// GET /api/v1/library/search - Search library templates
export async function GET(req: NextRequest) {
  const { libraryTemplateBusinessLogic } = getBusinessLogics()
  try {
    const { searchParams } = new URL(req.url)

    const query = searchParams.get("q") || searchParams.get("query")
    if (!query) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["Search query is required"],
          errorCodes: [ERROR_CODES.VALIDATION_ERROR],
        },
        { status: 400 },
      )
    }

    // Parse additional filter parameters
    const type = (searchParams.get("type") as any) || undefined
    const category = (searchParams.get("category") as any) || undefined
    const tags =
      searchParams.get("tags")?.split(",").filter(Boolean) || undefined
    const page = searchParams.get("page")
      ? parseInt(searchParams.get("page")!)
      : 1
    const limit = searchParams.get("limit")
      ? parseInt(searchParams.get("limit")!)
      : 20
    const sortBy = (searchParams.get("sortBy") as any) || "createdAt"
    const sortOrder = (searchParams.get("sortOrder") as any) || "DESC"

    const result = await libraryTemplateBusinessLogic.search(query, {
      type,
      category,
      tags,
      page,
      limit,
      sortBy,
      sortOrder,
    })

    return NextResponse.json({
      status: "success",
      data: result,
      message: "Search completed successfully",
    })
  } catch (error) {
    console.error("Library template search route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
