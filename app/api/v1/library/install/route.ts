import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { ERROR_CODES } from "@/app/api/error_codes"
import { z } from "zod"

const InstallTemplateSchema = z.object({
  templateId: z.string().min(1, "Template ID is required"),
  customizations: z.record(z.any()).optional(),
})

// POST /api/v1/library/install - Install a library template
export async function POST(req: NextRequest) {
  const { libraryTemplateBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()

    // Validate request body
    const validation = InstallTemplateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: validation.error.errors.map((err) => err.message),
          errorCodes: [ERROR_CODES.VALIDATION_ERROR],
        },
        { status: 400 },
      )
    }

    const { templateId, customizations } = validation.data

    const result = await libraryTemplateBusinessLogic.install(
      { templateId, customizations },
      context,
    )

    if (!result.success) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: [result.message],
          errorCodes: [ERROR_CODES.BUSINESS_LOGIC_ERROR],
        },
        { status: 400 },
      )
    }

    return NextResponse.json({
      status: "success",
      data: {
        installedId: result.installedId,
        type: result.type,
        message: result.message,
      },
      message: "Template installed successfully",
    })
  } catch (error) {
    console.error("Library template install route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
