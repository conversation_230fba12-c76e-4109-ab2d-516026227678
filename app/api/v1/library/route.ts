import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ERROR_CODES } from "@/app/api/error_codes"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

// GET /api/v1/library - Get all library templates with filtering and search
export async function GET(req: NextRequest) {
  const { libraryTemplateBusinessLogic } = getBusinessLogics()
  try {
    const { searchParams } = new URL(req.url)

    // Parse query parameters
    const search = searchParams.get("search") || undefined
    const type = (searchParams.get("type") as any) || undefined
    const category = (searchParams.get("category") as any) || undefined
    const tags =
      searchParams.get("tags")?.split(",").filter(Boolean) || undefined
    const isActive = searchParams.get("isActive")
      ? searchParams.get("isActive") === "true"
      : undefined
    const page = searchParams.get("page")
      ? parseInt(searchParams.get("page")!)
      : 1
    const limit = searchParams.get("limit")
      ? parseInt(searchParams.get("limit")!)
      : 20
    const sortBy = (searchParams.get("sortBy") as any) || "createdAt"
    const sortOrder = (searchParams.get("sortOrder") as any) || "DESC"

    const result = await libraryTemplateBusinessLogic.getAll({
      search,
      type,
      category,
      tags,
      isActive,
      page,
      limit,
      sortBy,
      sortOrder,
    })

    return NextResponse.json(new ResponseWrapper("success", result))
  } catch (error) {
    console.error("Library templates GET route error:", error)
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR],
      },
      { status: 500 },
    )
  }
}
