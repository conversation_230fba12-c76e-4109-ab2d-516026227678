import { processWebhook } from "@/app/api/v1/functions/webhook/impl"
import { loggedApiRoute } from "@/lib/logging/middleware"
import { driver } from "@/lib/repositories/LiveRedisDriver"
import { NextRequest, NextResponse } from "next/server"

export async function getTestConversationId(
  conversationId: string,
): Promise<string | null> {
  return await driver.get(`test-conversation-id-${conversationId}`)
}

export async function saveTestConversationId(
  conversationId: string,
  testConversationId: string,
) {
  await driver.set(`test-conversation-id-${conversationId}`, testConversationId)
}

export const POST = loggedApiRoute(
  async (req: NextRequest, context: { params: Promise<{ id: string }> }) => {
    const testConversationId = (await context.params).id
    const response = await processWebhook(req)
    const conversationId = response.data.conversationId
    if (conversationId && testConversationId) {
      await saveTestConversationId(conversationId, testConversationId)
    }

    if (response.status === "success") {
      return NextResponse.json(response, { status: 200 })
    } else {
      return NextResponse.json(response, { status: 400 })
    }
  },
)
