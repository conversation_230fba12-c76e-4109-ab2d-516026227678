import {
  RealtimeConversationRoom,
  RealtimeTestConversationRoom,
} from "@/lib/realtime/model"
import { SessionContext } from "@/lib/repositories/auth/types"
import { getTestConversationId } from "../webhook/test/route"
import { generateId } from "@/lib/utils/common"

export async function handleSendMessageAITestingConversation(
  conversationId: string,
  text: string,
  context: SessionContext,
) {
  const testConversationId = await getTestConversationId(conversationId)
  if (!testConversationId) {
    throw new Error("Test conversation not found")
  }
  await RealtimeTestConversationRoom.NEW_MESSAGE(testConversationId, {
    id: generateId("test-msg-ai"),
    content: text,
    senderId: "ai_assistant",
    senderName: "AI Assistant",
    isFromCustomer: false,
    isFromAI: true,
    timestamp: new Date().toISOString(),
    messageType: "TEXT",
  }).send()
}
