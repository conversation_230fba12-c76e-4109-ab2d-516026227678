import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { TestConversation } from "@/lib/repositories/testConversations"
import { createContextLogger } from "@/lib/logging"
import { loggedApiRoute } from "@/lib/logging/middleware"
import { saveSessionToContext } from "../devices/link/route"

export const GET = loggedApiRoute(async (req: NextRequest) => {
  const { testConversationBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const logger = createContextLogger({
      component: "test-conversations",
      userId: context.user.id,
      organizationId: context.organization?.id,
    })

    logger.info("Fetching test conversations")

    const result = await testConversationBusinessLogic.getAll(
      {
        page: 1,
        limit: 100,
        sort: [{ field: "createdAt", direction: "DESC" }],
      },
      context,
    )

    const testConversations: TestConversation[] = result.items

    logger.info("Test conversations fetched", {
      count: testConversations.length,
    })

    return NextResponse.json(
      new ResponseWrapper("success", {
        items: testConversations,
        total: result.total,
        page: 1,
        per_page: 100,
      }),
      { status: 200 },
    )
  } catch (error: any) {
    console.error("Test conversations GET error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch test conversations"],
        [ERROR_CODES.FETCH_FAILED],
      ),
      { status: 500 },
    )
  }
})

export const POST = loggedApiRoute(async (req: NextRequest) => {
  const { testConversationBusinessLogic } = getBusinessLogics()
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()

    const logger = createContextLogger({
      component: "test-conversations",
      userId: context.user.id,
      organizationId: context.organization?.id,
    })

    logger.info("Creating test conversation", {
      customerName: body.customerName,
      scenario: body.scenario,
    })

    // Create test conversation using testConversationBusinessLogic
    const testConversation = await testConversationBusinessLogic.create(
      {
        customerName: body.customerName,
        customerPhone: body.customerPhone,
        scenario: body.scenario,
        messages: [],
        status: "OPEN",
        isAiEnabled: true,
        scenarioDescription: body.scenarioDescription,
        sampleMessages: body.sampleMessages,
        title: body.title,
      },
      context,
    )

    await saveSessionToContext(
      "test-session-id_" + testConversation.id,
      context,
    )

    logger.info("Test conversation metadata created", {
      conversationId: testConversation.id,
      customerName: body.customerName,
      scenario: body.scenario,
    })

    return NextResponse.json(new ResponseWrapper("success", testConversation), {
      status: 201,
    })
  } catch (error: any) {
    console.error("Test conversations POST error:", error)
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create test conversation"],
        [ERROR_CODES.CREATE_FAILED],
      ),
      { status: 500 },
    )
  }
})
