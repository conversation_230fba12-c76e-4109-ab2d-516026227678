import {
  DatasourceBusinessLogicInterface,
  Datasource,
  DatasourceCreateInput,
  DatasourceUpdateInput,
} from "@/lib/repositories/datasources/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  DatasourceCreateSchema,
  DatasourceUpdateSchema,
} from "@/lib/validations/datasource"
import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"

type GetAllResultPaginated<T> = {
  items: T[]
  page: number
  total: number
}

// Create Datasource Implementation
export async function implHandleCreateDatasource(
  data: any,
  businessLogic: DatasourceBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = DatasourceCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const datasource = await businessLogic.create(
      validationResult.data,
      context,
    )

    return {
      status: 201,
      body: new ResponseWrapper("success", datasource),
    }
  } catch (error: any) {
    console.error("Create datasource error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create datasource. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Update Datasource Implementation
export async function implHandleUpdateDatasource(
  id: string,
  data: any,
  businessLogic: DatasourceBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = DatasourceUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const datasource = await businessLogic.update(
      id,
      validationResult.data,
      context,
    )

    if (!datasource) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Datasource not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", datasource),
    }
  } catch (error: any) {
    console.error("Update datasource error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update datasource. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Delete Datasource Implementation
export async function implHandleDeleteDatasource(
  id: string,
  businessLogic: DatasourceBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Datasource not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "Datasource deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete datasource error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete datasource. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Get Datasource by ID Implementation
export async function implHandleGetDatasource(
  id: string,
  businessLogic: DatasourceBusinessLogicInterface,
  context: SessionContext,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const datasource = await businessLogic.getById(id, context, includeDeleted)

    if (!datasource) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Datasource not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", datasource),
    }
  } catch (error: any) {
    console.error("Get datasource error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch datasource. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

// Get All Datasources Implementation
export async function implHandleGetAllDatasources(
  businessLogic: DatasourceBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof Datasource | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof Datasource | string
      value: Datasource[keyof Datasource] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<Datasource>>
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (typeof params.search !== "string") {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<Datasource>>(
            "failed",
            undefined,
            ["Search parameter must be a string"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    // Build query parameters
    const queryParams: any = {
      search: params?.search,
      includeDeleted: params?.includeDeleted || false,
    }

    // Add pagination if provided
    if (params?.page !== undefined && params?.limit !== undefined) {
      queryParams.offset = (params.page - 1) * params.limit
      queryParams.limit = params.limit
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params?.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(queryParams, context)

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<Datasource>>("success", {
        items: result.items,
        page: params?.page || 1,
        total: result.total,
      }),
    }
  } catch (error: any) {
    console.error("Get datasources error:", error)

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<Datasource>>(
        "failed",
        undefined,
        ["Failed to fetch datasources. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}
