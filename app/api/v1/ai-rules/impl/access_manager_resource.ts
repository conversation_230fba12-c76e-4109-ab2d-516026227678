import {
  GroupRoleResourceAccessManager,
  AccessRule,
} from "@/lib/repositories/AccessManager"

// Define allowed resources using wildcards
export const AccessManagerResource = [
  "* /ai-rules*",
  "GET /ai-rules",
  "GET /ai-rules/*",
  "POST /ai-rules",
  "PUT /ai-rules/*",
  "DELETE /ai-rules/*",
] as const

export type AIRuleAccessResource = (typeof AccessManagerResource)[number]

export async function setInitialAiRuleAccessOnRegister(
  groupId: string,
  accessManager: GroupRoleResourceAccessManager<AIRuleAccessResource>,
): Promise<void> {
  await accessManager.appendRules([
    // Admin – full access
    {
      resource: "* /ai-rules*",
      group: groupId,
      role: "admin",
      actions: ["read", "create", "update", "delete"],
    },

    {
      resource: "GET /ai-rules",
      group: groupId,
      role: "member",
      actions: ["read"],
    },
    {
      resource: "GET /ai-rules/*",
      group: groupId,
      role: "member",
      actions: ["read"],
    },
    {
      resource: "PUT /ai-rules/*",
      group: groupId,
      role: "member",
      actions: ["update"],
    },
  ])
}
