import {
  AiRuleBusinessLogicInterface,
  AiRule,
  AiRuleCreateInput,
  AiRuleUpdateInput,
} from "@/lib/repositories/aiRules/interface"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import {
  AiRuleCreateSchema,
  AiRuleUpdateSchema,
} from "@/lib/validations/aiRule"
import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"

// Create AiRule Implementation
export async function implHandleCreateAiRule(
  data: any,
  businessLogic: AiRuleBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate input data
    const validationResult = AiRuleCreateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const aiRules = await businessLogic.create(validationResult.data, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", aiRules),
    }
  } catch (error: any) {
    console.error("Create aiRules error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create aiRules. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Update AiRule Implementation
export async function implHandleUpdateAiRule(
  id: string,
  data: any,
  businessLogic: AiRuleBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate input data
    const validationResult = AiRuleUpdateSchema.safeParse(data)
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`,
      )

      return {
        status: 400,
        body: new ResponseWrapper("failed", undefined, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const aiRules = await businessLogic.update(
      id,
      validationResult.data,
      context,
    )

    if (!aiRules) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["AI Rule not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", aiRules),
    }
  } catch (error: any) {
    console.error("Update aiRules error:", error)

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update aiRules. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Delete AiRule Implementation
export async function implHandleDeleteAiRule(
  id: string,
  businessLogic: AiRuleBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const success = await businessLogic.delete(id, context, hardDelete)

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["AiRule not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        message: "AiRule deleted successfully",
      }),
    }
  } catch (error: any) {
    console.error("Delete aiRules error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete aiRules. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}

// Get AiRule by ID Implementation
export async function implHandleGetAiRule(
  id: string,
  businessLogic: AiRuleBusinessLogicInterface,
  context: SessionContext,
  includeDeleted: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    const aiRules = await businessLogic.getById(id, context, includeDeleted)

    if (!aiRules) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["AiRule not found"],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", aiRules),
    }
  } catch (error: any) {
    console.error("Get aiRules error:", error)

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch aiRules. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

interface GetAllResultPaginated<T> {
  items: T[]
  page: number
  total: number
}

// Get All AiRules Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllAiRules(
  businessLogic: AiRuleBusinessLogicInterface,
  context: SessionContext,
  params: {
    search?: string
    includeDeleted?: boolean
    page?: number
    limit?: number
    sort?: {
      field: keyof AiRule | string
      direction: "ASC" | "DESC"
    }[]
    filters?: {
      field: keyof AiRule | string
      value: AiRule[keyof AiRule] | any
    }[]
  },
): Promise<{
  status: number
  body: ResponseWrapper<GetAllResultPaginated<AiRule>>
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<AiRule>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === "") {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<AiRule>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Validate sort if provided
    if (params?.sort && params.sort.length > 0) {
      for (const sort of params.sort) {
        if (!sort.field || sort.field.trim() === "") {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<AiRule>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
        if (!["ASC", "DESC"].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<AiRule>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED],
            ),
          }
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    }

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim()
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters
    }

    // Add sort if provided
    if (params?.sort && params.sort.length > 0) {
      queryParams.sort = params.sort
    }

    const result = await businessLogic.getAll(queryParams, context)

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<AiRule>>("success", {
        ...result,
        page: queryParams.page,
      }),
    }
  } catch (error: any) {
    console.error("Get aiRules error:", error)

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<AiRule>>(
        "failed",
        undefined,
        ["Failed to fetch aiRules. Please try again."],
        [ERROR_CODES.FETCH_FAILED],
      ),
    }
  }
}

// Bulk Create AiRules Implementation
export async function implHandleBulkCreateAiRules(
  data: any[],
  businessLogic: AiRuleBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each item in the array
    const validatedData: AiRuleCreateInput[] = []
    for (let i = 0; i < data.length; i++) {
      const validationResult = AiRuleCreateSchema.safeParse(data[i])
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Item ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: new ResponseWrapper("failed", undefined, errors, [
            ERROR_CODES.VALIDATION_FAILED,
          ]),
        }
      }
      validatedData.push(validationResult.data)
    }

    const aiRules = await businessLogic.bulkCreate(validatedData, context)

    return {
      status: 201,
      body: new ResponseWrapper("success", aiRules),
    }
  } catch (error: any) {
    console.error("Bulk create aiRules error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk create aiRules. Please try again."],
        [ERROR_CODES.CREATE_FAILED],
      ),
    }
  }
}

// Bulk Update AiRules Implementation
export async function implHandleBulkUpdateAiRules(
  updates: { id: string; data: any }[],
  businessLogic: AiRuleBusinessLogicInterface,
  context: SessionContext,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: AiRuleUpdateInput }[] = []
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i]

      if (!update.id || typeof update.id !== "string") {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }

      const validationResult = AiRuleUpdateSchema.safeParse(update.data)
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(
          (err) => `Update ${i}: ${err.path.join(".")}: ${err.message}`,
        )
        return {
          status: 400,
          body: new ResponseWrapper("failed", undefined, errors, [
            ERROR_CODES.VALIDATION_FAILED,
          ]),
        }
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data,
      })
    }

    const updatedCount = await businessLogic.bulkUpdate(
      validatedUpdates,
      context,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", { updatedCount }),
    }
  } catch (error: any) {
    console.error("Bulk update aiRules error:", error)

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk update aiRules. Please try again."],
        [ERROR_CODES.UPDATE_FAILED],
      ),
    }
  }
}

// Bulk Delete AiRules Implementation
export async function implHandleBulkDeleteAiRules(
  ids: string[],
  businessLogic: AiRuleBusinessLogicInterface,
  context: SessionContext,
  hardDelete: boolean = false,
): Promise<{
  status: number
  body: ResponseWrapper<any>
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i]
      if (!id || typeof id !== "string" || id.trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }

    const deletedCount = await businessLogic.bulkDelete(
      ids,
      context,
      hardDelete,
    )

    return {
      status: 200,
      body: new ResponseWrapper("success", { deletedCount }),
    }
  } catch (error: any) {
    console.error("Bulk delete aiRules error:", error)

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND],
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk delete aiRules. Please try again."],
        [ERROR_CODES.DELETE_FAILED],
      ),
    }
  }
}
