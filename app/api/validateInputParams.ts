import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "./error_codes"
import { GetAllResultPaginated } from "./v1/ai-workflow-executions/impl"

export function validateParamsAndTrim<T>(params: {
  search?: string
  includeDeleted?: boolean
  page?: number
  limit?: number
  sort?: {
    field: keyof T | string
    direction: "ASC" | "DESC"
  }[]
  filters?: {
    field: keyof T | string
    value: T[keyof T] | any
  }[]
}) {
  if (params?.search !== undefined) {
    if (!params.search || params.search.trim() === "") {
      return {
        status: 400,
        body: new ResponseWrapper<GetAllResultPaginated<T>>(
          "failed",
          undefined,
          ["Search keyword cannot be empty"],
          [ERROR_CODES.VALIDATION_FAILED],
        ),
      }
    }
  }

  // Validate filters if provided
  if (params?.filters && params.filters.length > 0) {
    for (const filter of params.filters) {
      if (!filter.field || filter.field.toString().trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<T>>(
            "failed",
            undefined,
            ["Filter field cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }
  }

  // Validate sort if provided
  if (params?.sort && params.sort.length > 0) {
    for (const sort of params.sort) {
      if (!sort.field || sort.field.toString().trim() === "") {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<T>>(
            "failed",
            undefined,
            ["Sort field cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
      if (!["ASC", "DESC"].includes(sort.direction)) {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<T>>(
            "failed",
            undefined,
            ["Sort direction must be 'asc' or 'desc'"],
            [ERROR_CODES.VALIDATION_FAILED],
          ),
        }
      }
    }
  }

  // Build query parameters for the repository
  const queryParams: any = {
    includeDeleted: params?.includeDeleted,
    page: params?.page,
    limit: params?.limit,
  }

  // Add search if provided
  if (params?.search) {
    queryParams.search = params.search.trim()
  }

  // Add filters if provided
  if (params?.filters && params.filters.length > 0) {
    queryParams.filters = params.filters
  }

  // Add sort if provided
  if (params?.sort && params.sort.length > 0) {
    queryParams.sort = params.sort
  }

  return queryParams
}
