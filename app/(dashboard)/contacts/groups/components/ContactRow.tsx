"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, X, Check } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { Contact } from "@/lib/repositories/contacts"

interface ContactRowProps {
  contact: Contact
  isIncluded: boolean
  onToggle: (contactId: string, action: "include" | "exclude") => void
  showAddButton?: boolean
  showRemoveButton?: boolean
}

export function ContactRow({
  contact,
  isIncluded,
  onToggle,
  showAddButton = false,
  showRemoveButton = false
}: ContactRowProps) {
  const { t } = useLocalization("contact-groups", locales)

  const handleToggle = () => {
    if (isIncluded) {
      onToggle(contact.id, "exclude")
    } else {
      onToggle(contact.id, "include")
    }
  }

  return (
    <div
      className={`
        flex items-center gap-4 p-4 border rounded-lg transition-all duration-200 hover:shadow-md
        ${isIncluded
          ? "border-green-200 bg-green-50 hover:bg-green-100"
          : "border-gray-200 bg-white hover:bg-gray-50"
        }
      `}
    >
      {/* Contact Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <h4 className="font-medium text-gray-900 truncate">
              {contact.name}
            </h4>
            <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
              {contact.phone && (
                <span className="truncate">{contact.phone}</span>
              )}
              {contact.email && (
                <span className="truncate">{contact.email}</span>
              )}
            </div>
          </div>

          {/* Status Badge */}
          {isIncluded && (
            <div className="bg-green-100 text-green-700 border-green-200 border px-2 py-1 rounded-md text-xs flex items-center">
              <Check className="h-3 w-3 mr-1" />
              {t("status.included")}
            </div>
          )}
        </div>
      </div>

      {/* Action Button */}
      <div className="flex-shrink-0">
        {showAddButton && !isIncluded && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleToggle}
            className="border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400"
          >
            <Plus className="h-4 w-4 mr-1" />
            {t("buttons.add")}
          </Button>
        )}

        {showRemoveButton && isIncluded && (
          <Button
            size="sm"
            variant="outline"
            onClick={handleToggle}
            className="border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400"
          >
            <X className="h-4 w-4 mr-1" />
            {t("buttons.remove")}
          </Button>
        )}

        {!showAddButton && !showRemoveButton && (
          <Button
            size="sm"
            variant={isIncluded ? "outline" : "default"}
            onClick={handleToggle}
            className={
              isIncluded
                ? "border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400"
                : "border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400"
            }
          >
            {isIncluded ? (
              <>
                <X className="h-4 w-4 mr-1" />
                {t("buttons.remove")}
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-1" />
                {t("buttons.add")}
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  )
}
