"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDes<PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Info } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"

interface FormData {
  name: string
  description: string
  color: string
}

interface FormErrors {
  name?: string
  description?: string
  color?: string
}

interface BasicInfoTabProps {
  formData: FormData
  errors: FormErrors
  onInputChange: (field: keyof FormData, value: string) => void
}

export function BasicInfoTab({ formData, errors, onInputChange }: BasicInfoTabProps) {
  const { t } = useLocalization("contact-groups", locales)

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            {t("sections.basicInformation.title")}
          </CardTitle>
          <CardDescription>
            {t("sections.basicInformation.description")}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium">
              {t("fields.name.label")} <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => onInputChange("name", e.target.value)}
              placeholder={t("fields.name.placeholder")}
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          {/* Description Field */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              {t("fields.description.label")}
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => onInputChange("description", e.target.value)}
              placeholder={t("fields.description.placeholder")}
              rows={3}
              className={errors.description ? "border-red-500" : ""}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Appearance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="w-5 h-5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500" />
            {t("sections.appearance.title")}
          </CardTitle>
          <CardDescription>
            {t("sections.appearance.description")}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Color Field */}
          <div className="space-y-2">
            <Label htmlFor="color" className="text-sm font-medium">
              {t("fields.color.label")}
            </Label>
            <div className="flex items-center gap-3">
              <input
                type="color"
                id="color"
                value={formData.color || "#3b82f6"}
                onChange={(e) => onInputChange("color", e.target.value)}
                className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
              />
              <Input
                type="text"
                value={formData.color}
                onChange={(e) => onInputChange("color", e.target.value)}
                placeholder={t("fields.color.placeholder")}
                className={`flex-1 ${errors.color ? "border-red-500" : ""}`}
              />
            </div>
            {errors.color && (
              <p className="text-sm text-red-500">{errors.color}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {t("fields.color.description")}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
