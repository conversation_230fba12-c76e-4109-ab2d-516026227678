"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Users, Search, Loader2, Plus, X, Check } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { Contact } from "@/lib/repositories/contacts"
import { ContactRow } from "./ContactRow"

interface ContactChanges {
  toAdd: string[]
  toRemove: string[]
}

interface ContactsTabProps {
  searchTerm: string
  setSearchTerm: (term: string) => void
  searchResults: Contact[]
  loadingSearch: boolean
  currentContacts: Contact[]
  loadingCurrentContacts: boolean
  isContactIncluded: (contactId: string) => boolean
  toggleContact: (contactId: string, action: "include" | "exclude") => void
  contactChanges: ContactChanges
  isEditMode: boolean
}

export function ContactsTab({
  searchTerm,
  setSearchTerm,
  searchResults,
  loadingSearch,
  currentContacts,
  loadingCurrentContacts,
  isContactIncluded,
  toggleContact,
  contactChanges,
  isEditMode
}: ContactsTabProps) {
  const { t } = useLocalization("contact-groups", locales)

  // Filter search results to exclude contacts that are already included
  const filteredSearchResults = searchResults

  return (
    <div className="space-y-6">
      {/* Current Contacts (Edit Mode Only) */}
      {isEditMode && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t("sections.current_contacts")}
              <span className="text-sm font-normal text-muted-foreground">
                ({currentContacts.length})
              </span>
            </CardTitle>
            <CardDescription>
              {t("sections.current_contacts_description")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loadingCurrentContacts ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : currentContacts.length > 0 ? (
              <div className="space-y-3">
                {currentContacts.map((contact) => (
                  <ContactRow
                    key={contact.id}
                    contact={contact}
                    isIncluded={isContactIncluded(contact.id)}
                    onToggle={toggleContact}
                    showRemoveButton={true}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {t("messages.no_current_contacts")}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Add Contacts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            {isEditMode ? t("sections.add_contacts") : t("sections.select_contacts")}
          </CardTitle>
          <CardDescription>
            {isEditMode
              ? t("sections.add_contacts_description")
              : t("sections.select_contacts_description")
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder={t("placeholders.search_contacts")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Search Results */}
          {searchTerm.trim() && (
            <div className="space-y-3">
              {loadingSearch ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : filteredSearchResults.length > 0 ? (
                <>
                  <div className="text-sm text-muted-foreground">
                    {t("messages.search_results", { count: filteredSearchResults.length })}
                  </div>
                  {filteredSearchResults.map((contact) => (
                    <ContactRow
                      key={contact.id}
                      contact={contact}
                      isIncluded={isContactIncluded(contact.id)}
                      onToggle={toggleContact}
                      showAddButton={true}
                      showRemoveButton={true}
                    />
                  ))}
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {t("messages.no_search_results")}
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          {!searchTerm.trim() && (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>{t("messages.search_instruction")}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Changes Summary */}
      {(contactChanges.toAdd.length > 0 || contactChanges.toRemove.length > 0) && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Check className="h-5 w-5" />
              {t("sections.pending_changes")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              {contactChanges.toAdd.length > 0 && (
                <div className="flex items-center gap-2 text-green-700">
                  <Plus className="h-4 w-4" />
                  {t("messages.contacts_to_add", { count: contactChanges.toAdd.length })}
                </div>
              )}
              {contactChanges.toRemove.length > 0 && (
                <div className="flex items-center gap-2 text-red-700">
                  <X className="h-4 w-4" />
                  {t("messages.contacts_to_remove", { count: contactChanges.toRemove.length })}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
