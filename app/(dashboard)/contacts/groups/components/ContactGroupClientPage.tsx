"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Loader2 } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { Contact } from "@/lib/repositories/contacts"
import { ContactGroup } from "@/lib/repositories/contactGroups/interface"
import { ContactsAPI } from "@/lib/services"
import { BasicInfoTab } from "./BasicInfoTab"
import { ContactsTab } from "./ContactsTab"
import { DeleteDialog } from "./DeleteDialog"
import { TabsContainer } from "./TabsContainer"
interface ContactGroupClientPageProps {
  initialData?: ContactGroup
  currentContacts: Contact[]
  loadMoreCurrentContacts: () => void
  loadingCurrentContacts: boolean
  onSave: (formData: FormData, contactChanges: ContactChanges) => Promise<void>
  title: string
  subtitle: string
  submitButtonText: string
  showDeleteButton?: boolean
  onDelete?: () => Promise<void>
}

interface FormData {
  name: string
  description: string
  color: string
}

interface FormErrors {
  name?: string
  description?: string
  color?: string
}

interface ContactChanges {
  toAdd: string[]
  toRemove: string[]
}

export default function ContactGroupClientPage({
  initialData,
  currentContacts,
  loadMoreCurrentContacts,
  loadingCurrentContacts,
  onSave,
  title,
  subtitle,
  submitButtonText,
  showDeleteButton = false,
  onDelete
}: ContactGroupClientPageProps) {
  const { t } = useLocalization("contact-groups", locales)
  const router = useRouter()

  // Form state
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    color: "",
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Contact management state
  const [searchTerm, setSearchTerm] = useState("")
  const [searchResults, setSearchResults] = useState<Contact[]>([])
  const [loadingSearch, setLoadingSearch] = useState(false)
  const [contactChanges, setContactChanges] = useState<ContactChanges>({
    toAdd: [],
    toRemove: []
  })

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name || "",
        description: initialData.description || "",
        color: initialData.color || "",
      })
      setIsLoading(false)
    } else {
      setIsLoading(false)
    }
  }, [initialData])

  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchResults([])
      return
    }

    const timeoutId = setTimeout(async () => {
      try {
        setLoadingSearch(true)
        const response = await ContactsAPI.All({
          search: searchTerm,
          page: 1,
          per_page: 20,
        }).request()
        setSearchResults(response.items || [])
      } catch (error) {
        console.error("Search failed:", error)
        setSearchResults([])
      } finally {
        setLoadingSearch(false)
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = t("errors.nameRequired")
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setHasUnsavedChanges(true)

    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const toggleContact = (contactId: string, action: "include" | "exclude") => {
    setContactChanges(prev => {
      const newChanges = { ...prev }

      switch (action) {
        case "include": {
          if (!newChanges.toAdd.includes(contactId)) {
            newChanges.toAdd.push(contactId)
            newChanges.toRemove = newChanges.toRemove.filter(id => id !== contactId)
          }
          break
        }
        case "exclude": {
          if (!newChanges.toRemove.includes(contactId)) {
            newChanges.toRemove.push(contactId)
            newChanges.toAdd = newChanges.toAdd.filter(id => id !== contactId)
          }
          break
        }
      }

      return newChanges
    })

    if (contactChanges.toAdd.length > 0 || contactChanges.toRemove.length > 0) {
      setHasUnsavedChanges(true)
    } else {
      setHasUnsavedChanges(false)
    }
  }

  const isContactIncluded = (contactId: string): boolean => {
    const isCurrentlyIncluded = currentContacts.some(c => c.id === contactId)
    const isBeingAdded = contactChanges.toAdd.includes(contactId)
    const isBeingRemoved = contactChanges.toRemove.includes(contactId)

    if (isBeingAdded) return true
    if (isBeingRemoved) return false
    return isCurrentlyIncluded
  }



  const handleSave = async () => {
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      await onSave(formData, contactChanges)
      router.push("/contacts/groups")
    } catch (error) {
      console.error("Failed to save contact group:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async () => {
    if (!onDelete) return

    setIsDeleting(true)

    try {
      await onDelete()
      router.push("/contacts/groups")
    } catch (error) {
      console.error("Failed to delete contact group:", error)
    } finally {
      setIsDeleting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6 px-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/contacts/groups")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t("buttons.back")}
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {title}
            </h1>
            <p className="text-muted-foreground">
              {subtitle}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {showDeleteButton && (
            <DeleteDialog
              onDelete={handleDelete}
              isDeleting={isDeleting}
              groupName={formData.name}
            />
          )}
        </div>
      </div>

      <div className="space-y-6">
        <TabsContainer
          basicInfoTab={
            <BasicInfoTab
              formData={formData}
              errors={errors}
              onInputChange={handleInputChange}
            />
          }
          contactsTab={
            <ContactsTab
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              searchResults={searchResults}
              loadingSearch={loadingSearch}
              currentContacts={currentContacts}
              loadingCurrentContacts={loadingCurrentContacts}
              isContactIncluded={isContactIncluded}
              toggleContact={toggleContact}
              contactChanges={contactChanges}
              isEditMode={true}
            />
          }
          onSave={handleSave}
          isSubmitting={isSubmitting}
          hasUnsavedChanges={hasUnsavedChanges}
          submitButtonText={submitButtonText}
        />
      </div>
    </div>
  )
}
