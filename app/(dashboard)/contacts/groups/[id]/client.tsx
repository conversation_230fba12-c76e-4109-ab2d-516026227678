"use client"

import { useState, useEffect } from "react"
import { toast } from "@/hooks/use-toast"
import ContactGroupClientPage from "../components/ContactGroupClientPage"
import { ContactGroupsAPI } from "@/lib/services/contactGroupsApi"
import { ContactGroup } from "@/lib/repositories/contactGroups/interface"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "../locales"
import { Contact } from "@/lib/repositories/contacts"

interface ClientPageProps {
  id: string
}

interface FormData {
  name: string
  description: string
  color: string
}

interface ContactChanges {
  toAdd: string[]
  toRemove: string[]
}

export default function ClientPage({ id }: ClientPageProps) {
  const { t } = useLocalization("contact-groups", locales)
  const [initialData, setInitialData] = useState<ContactGroup | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadContactGroup = async () => {
      try {
        const response = await ContactGroupsAPI.Detail(id).request()
        setInitialData(response)
      } catch (error) {
        console.error("Failed to load contact group:", error)
        toast({
          title: "Error",
          description: t("errors.loadFailed"),
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadContactGroup()
  }, [id])

  const [currentContacts, setCurrentContacts] = useState<Contact[]>([])
  const [currentContactPage, setCurrentContactPage] = useState(1)
  const [loadingCurrentContacts, setLoadingCurrentContacts] = useState(false)

  const fetchCurrentContacts = async (page: number) => {
    try {
      setLoadingCurrentContacts(true)
      const response = await ContactGroupsAPI.Contacts(id, {
        page: page,
        per_page: 20
      }).request()
      setCurrentContacts(prev => [...prev, ...response.items || []])
      setCurrentContactPage(page)
    } catch (error) {
      console.error("Failed to fetch current contacts:", error)
    } finally {
      setLoadingCurrentContacts(false)
    }
  }

  useEffect(() => {
    fetchCurrentContacts(1)
  }, [id])

  const handleSave = async (formData: FormData, contactChanges: ContactChanges) => {
    try {
      await ContactGroupsAPI.Update(id, {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        color: formData.color || undefined,
      }).request()

      // Apply contact changes if any
      if (contactChanges.toAdd.length > 0 || contactChanges.toRemove.length > 0) {
        await ContactGroupsAPI.UpdateGroupContacts(id, {
          added: contactChanges.toAdd,
          deleted: contactChanges.toRemove,
        }).request()
      }

      toast({
        title: "Success",
        description: t("buttons.update") + " successful",
      })
    } catch (error) {
      console.error("Failed to update contact group:", error)
      toast({
        title: "Error",
        description: t("errors.updateFailed"),
        variant: "destructive",
      })
      throw error
    }
  }

  const handleDelete = async () => {
    try {
      await ContactGroupsAPI.Delete(id).request()
      toast({
        title: "Success",
        description: "Contact group deleted successfully",
      })
    } catch (error) {
      console.error("Failed to delete contact group:", error)
      toast({
        title: "Error",
        description: "Failed to delete contact group",
        variant: "destructive",
      })
      throw error
    }
  }

  if (isLoading) {
    return <div>Loading...</div>
  }

  return (
    <ContactGroupClientPage
      initialData={initialData || undefined}
      currentContacts={currentContacts}
      loadMoreCurrentContacts={() => fetchCurrentContacts(currentContactPage + 1)}
      loadingCurrentContacts={loadingCurrentContacts}
      onSave={handleSave}
      title={t("edit_title")}
      subtitle={t("edit_subtitle")}
      submitButtonText={t("buttons.update")}
      showDeleteButton={true}
      onDelete={handleDelete}
    />
  )
}
